import { createRequire } from 'node:module';
import path from 'node:path';
import * as Repack from '@callstack/repack';
import TerserPlugin from 'terser-webpack-plugin';
import { getSharedDependencies } from '@ac-mobile/sdk';

const dirname = Repack.getDirname(import.meta.url);
const { resolve } = createRequire(import.meta.url);
const STANDALONE = Boolean(process.env.STANDALONE);

export default env => {
  const {
    mode = 'development',
    context = dirname,
    entry = './index.js',
    platform = process.env.PLATFORM,
    minimize = mode === 'production',
    devServer = undefined,
    bundleFilename = undefined,
    sourceMapFilename = undefined,
    assetsPath = undefined,
    reactNativePath = resolve('react-native'),
  } = env;

  if (!platform) {
    throw new Error('Missing platform');
  }

  process.env.BABEL_ENV = mode;

  return {
    mode,

    devtool: false,
    context,

    entry: [
      ...Repack.getInitializationEntries(reactNativePath, {
        hmr: devServer && devServer.hmr,
      }),
      entry,
    ],
    resolve: {
      ...Repack.getResolveOptions(platform),
      alias: {
        'react-native': reactNativePath,
      },
    },
    output: {
      clean: true,
      hashFunction: 'xxhash64',
      path: path.join(dirname, 'build/generated', platform),
      filename: 'index.bundle',
      chunkFilename: '[name].chunk.bundle',
      publicPath: Repack.getPublicPath({ platform, devServer }),

      uniqueName: 'sas-citizen',
    },
    optimization: {
      minimize,
      minimizer: [
        new TerserPlugin({
          test: /\.(js)?bundle(\?.*)?$/i,
          extractComments: false,
          terserOptions: {
            format: {
              comments: false,
            },
          },
        }),
      ],
      chunkIds: 'named',
    },
    module: {
      rules: [
        {
          test: /\.[cm]?[jt]sx?$/,
          include: [
            /node_modules(.*[/\\])+react-native/,
            /node_modules(.*[/\\])+@react-native/,
            /node_modules(.*[/\\])+react-native-svg/,
            /node_modules(.*[/\\])+css-select/,
            /node_modules(.*[/\\])+@react-navigation/,
            /node_modules(.*[/\\])+@ac-mobile\/common/,
            /node_modules(.*[/\\])+@react-native-community/,
            /node_modules(.*[/\\])+jwt-decode/,
            /node_modules(.*[/\\])+lottie-react-native/,
            /node_modules(.*[/\\])+expo/,
            /node_modules(.*[/\\])+pretty-format/,
            /node_modules(.*[/\\])+metro/,
            /node_modules(.*[/\\])+abort-controller/,
            /node_modules(.*[/\\])+@callstack[/\\]repack/,
            /node_modules(.*[/\\])+nativewind/,
            /node_modules(.*[/\\])+semver/,
            /node_modules(.*[/\\])+react-freeze/,
            /node_modules(.*[/\\])+node-forge/,
            /node_modules(.*[/\\])+node-libs-react-native/,
            /node_modules(.*[/\\])+react-native-paper-dates/,
            /node_modules(.*[/\\])+react-native-reanimated\//,
            /node_modules(.*[/\\])+@gorhom\/portal/,
            /node_modules(.*[/\\])+@gorhom\/bottom-sheet/,
            /node_modules(.*[/\\])+color/,
            /node_modules(.*[/\\])+react-query/,
            /node_modules(.*[/\\])+react-native-linear-gradient/,
            /node_modules(.*[/\\])+react-hook-form/,
            /node_modules(.*[/\\])+@hookform[/\\]resolvers/,
            /node_modules(.*[/\\])+yup/,
          ],
          use: 'babel-loader',
        },
        {
          test: /\.[jt]sx?$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              /** Add React Refresh transform only when HMR is enabled. */
              plugins:
                devServer && devServer.hmr
                  ? ['module:react-refresh/babel']
                  : undefined,
            },
          },
        },
        {
          test: Repack.getAssetExtensionsRegExp(
            Repack.ASSET_EXTENSIONS.filter(ext => ext !== 'svg'),
          ),
          exclude: [
            path.join(dirname, 'src/assets/localAssets'),
            path.join(dirname, 'src/assets/inlineAssets'),
            path.join(dirname, 'src/assets/remoteAssets'),
          ],
          use: {
            loader: '@callstack/repack/assets-loader',
            options: {
              platform,
              devServerEnabled: Boolean(devServer),
              scalableAssetExtensions: Repack.SCALABLE_ASSETS,
            },
          },
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: '@svgr/webpack',
              options: {
                native: true,
                dimensions: false,
              },
            },
          ],
        },
        {
          test: Repack.getAssetExtensionsRegExp(
            Repack.ASSET_EXTENSIONS.filter(ext => ext !== 'svg'),
          ),
          include: [path.join(dirname, 'src/assets/localAssets')],
          use: {
            loader: '@callstack/repack/assets-loader',
            options: {
              platform,
              devServerEnabled: Boolean(devServer),
              scalableAssetExtensions: Repack.SCALABLE_ASSETS,
            },
          },
        },
        {
          test: Repack.getAssetExtensionsRegExp(
            Repack.ASSET_EXTENSIONS.filter(ext => ext !== 'svg'),
          ),
          include: [path.join(dirname, 'src/assets/inlineAssets')],
          use: {
            loader: '@callstack/repack/assets-loader',
            options: {
              platform,
              devServerEnabled: Boolean(devServer),
              scalableAssetExtensions: Repack.SCALABLE_ASSETS,
              inline: true,
            },
          },
        },
        {
          test: Repack.getAssetExtensionsRegExp(
            Repack.ASSET_EXTENSIONS.filter(ext => ext !== 'svg'),
          ),
          include: [path.join(dirname, 'src/assets/remoteAssets')],
          use: {
            loader: '@callstack/repack/assets-loader',
            options: {
              platform,
              devServerEnabled: Boolean(devServer),
              scalableAssetExtensions: Repack.SCALABLE_ASSETS,
              remote: {
                enabled: true,
                publicPath: 'http://localhost:9998/remote-assets',
              },
            },
          },
        },
        {
          test: Repack.getAssetExtensionsRegExp(Repack.ASSET_EXTENSIONS),
          use: {
            loader: '@callstack/repack/assets-loader',
            options: {
              platform,
              devServerEnabled: Boolean(devServer),
              inline: true,
              scalableAssetExtensions: Repack.SCALABLE_ASSETS,
            },
          },
        },
      ],
    },
    plugins: [
      new Repack.RepackPlugin({
        context,
        mode,
        platform,
        devServer,
        output: {
          bundleFilename,
          sourceMapFilename,
          assetsPath,
        },
      }),
      new Repack.plugins.ModuleFederationPlugin({
        name: 'citizen',
        exposes: {
          './App': './src/navigation/MainNavigator',
          './TermsScreen': './src/screens/TermsScreen',
        },
        shared: {
          ...getSharedDependencies({ eager: STANDALONE }),
          'react-query': {
            singleton: true,
            requiredVersion: '3.39.3',
            eager: true,
          },
          '@gorhom/bottom-sheet': {
            singleton: true,
            requiredVersion: '5.1.1',
            eager: true,
          },
          'react-native-gesture-handler': {
            eager: true,
            singleton: true,
            requiredVersion: '2.21.2',
          },
          moment: {
            eager: true,
            singleton: true,
            requiredVersion: '2.30.1',
          },
        },
      }),
      new Repack.plugins.CodeSigningPlugin({
        enabled: mode === 'production',
        privateKeyPath: path.join('..', '..', 'code-signing.pem'),
      }),
    ],
  };
};
