import React, {useState} from 'react';
import {useMutation} from 'react-query';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Platform,
} from 'react-native';

import {Button, useTheme, Avatar, Portal, Dialog} from 'react-native-paper';
import {StepMenuVertical, IOptionMenuItem} from '../../components/AcStepMenu';
import {FOOTER_HEIGHT} from '../../styles';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {MainStackParamList} from '../../navigation/MainNavigator';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {useCommonProcedure} from '../../stores';
import {template} from '../../requester-biz-service/apis/template-api';
import {getCitizenCode, useUserStore} from '../../stores/user.store';
import {useForm, FormProvider} from 'react-hook-form';
import {RHFTextField} from '../../components/RHFCustomComponent/RHFTextField';
import {RHFDatePicker} from '../../components/RHFCustomComponent';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {commonStyle} from './commonStyle';

const optionSteps: IOptionMenuItem[] = [
  {id: 1, name: 'Chọn vai trò thực hiện'},
  {id: 2, name: 'Kê khai biểu mẫu điện tử'},
  {id: 3, name: 'Đính kèm giấy tờ'},
  {id: 4, name: 'Nhận kết quả & thanh toán'},
];

const roleOptions = [
  {label: 'Bản thân', value: 'Bản thân', icon: 'account'},
  {label: 'Khai hộ', value: 'Khai hộ', icon: 'account-multiple'},
  {label: 'Doanh Nghiệp', value: 'Doanh Nghiệp', icon: 'domain'},
];

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');
const isSmallScreen = screenWidth < 375;
const isTablet = screenWidth > 768;

export const StepRoleChoose = () => {
  const methods = useForm({mode: 'onChange'});
  const {setValue, watch} = methods;
  const theme = useTheme();
  const [currentStep] = useState<number>(1);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [showKhaiHoDialog, setShowKhaiHoDialog] = useState<boolean>(false);
  const [showDoanhNghiepDialog, setShowDoanhNghiepDialog] =
    useState<boolean>(false);
  const nav = useNavigation<NavigationProp<MainStackParamList>>();
  const addItemToArray = useSubmitFlow(state => state.addItemToArray);
  const removeItemFromArray = useSubmitFlow(state => state.removeItemFromArray);
  const hasFormFieldInTemplateDetail = useCommonProcedure(
    state => state.hasFormFieldInTemplateDetail,
  );
  const {userProfile} = useUserStore();
  const forwardData = useCommonProcedure(state => state.forwardData);
  const formId = useCommonProcedure(state => state.formId);
  const formName = useCommonProcedure(state => state.formName);
  const templateDetail = useCommonProcedure(state => state.templateDetail);
  const displayName = useUserStore(state => state.getDisplayName());
  const stepOneFormData = useSubmitFlow(state => state.stepOneFormData);

  const initUserFormMutation = useMutation(
    async (payload: any) => {
      return await template.initUserForm({data: payload});
    },
    {
      onSuccess: data => {
        nav.navigate('StepTwo', {refId: data?.data.data.userFormId});
      },
    },
  );

  // Watch form fields for Khai hộ
  const uyQuyenHoTen = watch('uyQuyenHoTen') || '';
  const uyQuyenNgaySinh = watch('uyQuyenNgaySinh') || '';
  const uyQuyenSoGiayTo = watch('uyQuyenSoGiayTo') || '';
  const uyQuyenMoiQuanHe = watch('uyQuyenMoiQuanHe') || '';
  // Watch form fields for Doanh Nghiệp
  const doanhNghiepTen = watch('doanhNghiepTen') || '';
  const doanhNghiepSoDKKD = watch('doanhNghiepSoDKKD') || '';
  const doanhNghiepMaSoThue = watch('doanhNghiepMaSoThue') || '';
  const doanhNghiepDiaChi = watch('doanhNghiepDiaChi') || '';
  const doanhNghiepSoDienThoai = watch('doanhNghiepSoDienThoai') || '';

  const isKhaiHoFormValid =
    uyQuyenHoTen.trim() &&
    uyQuyenNgaySinh &&
    uyQuyenSoGiayTo.trim() &&
    uyQuyenMoiQuanHe.trim();
  const isDoanhNghiepFormValid =
    doanhNghiepTen.trim() &&
    doanhNghiepSoDKKD.trim() &&
    doanhNghiepMaSoThue.trim() &&
    doanhNghiepDiaChi.trim() &&
    doanhNghiepSoDienThoai.trim();

  const handleRoleSelect = (role: string) => {
    setSelectedRole(role);
    if (role === 'Khai hộ') {
      setShowKhaiHoDialog(true);
    } else if (role === 'Doanh Nghiệp') {
      setShowDoanhNghiepDialog(true);
    }
  };

  const handleKhaiHoDialogClose = () => {
    setShowKhaiHoDialog(false);
    // Reset form values for Khai hộ
    setValue('uyQuyenHoTen', '');
    setValue('uyQuyenNgaySinh', '');
    setValue('uyQuyenSoGiayTo', '');
    setValue('uyQuyenMoiQuanHe', '');
  };

  const handleDoanhNghiepDialogClose = () => {
    setShowDoanhNghiepDialog(false);
    // Reset form values for Doanh Nghiệp
    setValue('doanhNghiepTen', '');
    setValue('doanhNghiepSoDKKD', '');
    setValue('doanhNghiepMaSoThue', '');
    setValue('doanhNghiepDiaChi', '');
    setValue('doanhNghiepSoDienThoai', '');
  };

  const handleSubmit = () => {
    // add email and password to item at submit flow define
    const emailItem = templateDetail?.data.find(
      (item: {dataType: string}) => item.dataType === 'email',
    );
    if (emailItem) {
      addItemToArray({
        code: emailItem.code,
        name: emailItem.name,
        dataType: emailItem.dataType,
        required: emailItem.required,
        value: stepOneFormData?.email || '',
      });
    }
    const phoneItem = templateDetail?.data.find(
      (item: {dataType: string}) => item.dataType === 'phone',
    );
    if (phoneItem) {
      addItemToArray({
        code: phoneItem.code,
        name: phoneItem.name,
        dataType: phoneItem.dataType,
        required: phoneItem.required,
        value: stepOneFormData?.phoneNumber || '',
      });
    }
    // Next logic
    if (selectedRole === 'Khai hộ') {
      // Convert uyQuyenNgaySinh to YYYY-MM-DD if needed
      let formattedUyQuyenNgaySinh = uyQuyenNgaySinh;
      if (uyQuyenNgaySinh) {
        // If it's a Moment object
        if (uyQuyenNgaySinh._isAMomentObject) {
          formattedUyQuyenNgaySinh = uyQuyenNgaySinh.format('YYYY-MM-DD');
        } else if (typeof uyQuyenNgaySinh === 'string') {
          if (uyQuyenNgaySinh.includes('/')) {
            const parts = uyQuyenNgaySinh.split('/');
            if (parts.length === 3) {
              const yyyy = parts[2];
              const mm = parts[1].padStart(2, '0');
              const dd = parts[0].padStart(2, '0');
              formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
            }
          } else {
            // Parse ISO or other string formats
            const date = new Date(uyQuyenNgaySinh);
            if (!isNaN(date.getTime())) {
              const yyyy = date.getFullYear();
              const mm = String(date.getMonth() + 1).padStart(2, '0');
              const dd = String(date.getDate()).padStart(2, '0');
              formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
            } else if (
              uyQuyenNgaySinh.includes('-') &&
              uyQuyenNgaySinh.length >= 10
            ) {
              // Fallback: take first 10 chars
              formattedUyQuyenNgaySinh = uyQuyenNgaySinh.substring(0, 10);
            }
          }
        } else if (uyQuyenNgaySinh instanceof Date) {
          // If it's a Date object
          const yyyy = uyQuyenNgaySinh.getFullYear();
          const mm = String(uyQuyenNgaySinh.getMonth() + 1).padStart(2, '0');
          const dd = String(uyQuyenNgaySinh.getDate()).padStart(2, '0');
          formattedUyQuyenNgaySinh = `${yyyy}-${mm}-${dd}`;
        }
      }
      removeItemFromArray('_thongTinUyQuyenDoanhNghiep');
      removeItemFromArray('_taiLieuUyQuyenDoanhNghiep');
      addItemToArray({
        code: '_taiLieuUyQuyenCaNhan',
        name: 'Tài liệu uỷ quyền cá nhân',
        dataType: 'uri',
        required: true,
        isAttachmentUri: true,
      });

      addItemToArray({
        code: '_thongTinUyQuyenCaNhan',
        name: 'UyQuyen',
        dataType: 'json',
        isExternalData: true,
        required: true,
        value: {
          HoTen: uyQuyenHoTen,
          NgayThangNamSinh: formattedUyQuyenNgaySinh,
          QuanHe: uyQuyenMoiQuanHe,
          SoGiayTo: uyQuyenSoGiayTo,
        },
      });
    } else if (selectedRole === 'Doanh Nghiệp') {
      removeItemFromArray('_thongTinUyQuyenCaNhan');
      removeItemFromArray('_taiLieuUyQuyenCaNhan');
      addItemToArray({
        code: '_taiLieuUyQuyenDoanhNghiep',
        name: 'Tài liệu uỷ quyền doanh nghiệp',
        dataType: 'uri',
        required: true,
        isAttachmentUri: true,
      });
      addItemToArray({
        code: '_thongTinUyQuyenDoanhNghiep',
        name: 'UyQuyen',
        dataType: 'json',
        isExternalData: true,
        required: true,
        value: {
          TenDoanhNghiep: doanhNghiepTen,
          GiayChungNhan_DKKD: doanhNghiepSoDKKD,
          Email_SDT: doanhNghiepSoDienThoai,
          MaSoThue: doanhNghiepMaSoThue,
          ThongTinLienHe: doanhNghiepDiaChi,
        },
      });
    } else if (selectedRole === 'Bản thân') {
      removeItemFromArray('_thongTinUyQuyenCaNhan');
      removeItemFromArray('_taiLieuUyQuyenCaNhan');
      removeItemFromArray('_thongTinUyQuyenDoanhNghiep');
      removeItemFromArray('_taiLieuUyQuyenDoanhNghiep');
    }
    if (hasFormFieldInTemplateDetail && templateDetail) {
      // Format DateOfProvision to yyyy-MM-dd
      let formattedDate = '';
      if (stepOneFormData?.DateOfProvision) {
        const date = new Date(stepOneFormData.DateOfProvision);
        if (!isNaN(date.getTime())) {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          formattedDate = `${yyyy}-${mm}-${dd}`;
        }
      }
      // Format DateOfBirth from DD/MM/YYYY to YYYY-MM-DD
      console.log(stepOneFormData?.DateOfProvision);
      let formattedBirthDate = '';
      if (stepOneFormData?.DateOfBirth) {
        const parts = stepOneFormData.DateOfBirth.split('/');
        if (parts.length === 3) {
          const yyyy = parts[2];
          const mm = parts[1].padStart(2, '0');
          const dd = parts[0].padStart(2, '0');
          formattedBirthDate = `${yyyy}-${mm}-${dd}`;
        }
      }
      // Get current date in YYYY-MM-DD format
      const now = new Date();
      const nowYYYY = now.getFullYear();
      const nowMM = String(now.getMonth() + 1).padStart(2, '0');
      const nowDD = String(now.getDate()).padStart(2, '0');
      const currentDate = `${nowYYYY}-${nowMM}-${nowDD}`;
      const payload = {
        formId: formId || '',
        name: formName || '',
        jsonData: {
          noiGui: forwardData?.TenDonViThucHien || '',
          nycHoTen: displayName || '',
          nycDiaChiCuTru: '',
          nycLoaiGiayToTuyThan: stepOneFormData?.DocumentType.value.value,
          nycSoGiayTo: stepOneFormData?.CitizenCode,
          nycNgayCapGiayTo: formattedDate,
          nycNoiCapGiayTo: stepOneFormData?.PlaceOfIssues.value.value,
          nycGioiTinh: stepOneFormData?.Sex.value.value,
          nycNgaySinh: formattedBirthDate,
          nycQuocTich: stepOneFormData?.Nationality.value.value || '',
          nycDanToc: stepOneFormData?.Nation.value.value,
          nycTonGiao: stepOneFormData?.Religion.value.value,
          nycEmail: stepOneFormData?.email || '',
          nycSoDienThoai: stepOneFormData?.phoneNumber || '',
          ngayTao: currentDate,
          quanHe: selectedRole,
          tenCapThucHien: forwardData?.CapThucHien,
        },

        ownerId: getCitizenCode(userProfile), // Fill with actual ownerId if available
      };

      initUserFormMutation.mutate(payload);
      // nav.navigate('StepTwo');
    } else {
      nav.navigate('StepThree');
    }
  };

  const canProceed = () => {
    if (!selectedRole) return false;
    if (selectedRole === 'Bản thân') return true;
    if (selectedRole === 'Khai hộ') return isKhaiHoFormValid;
    if (selectedRole === 'Doanh nghiệp') return isDoanhNghiepFormValid;
    return false;
  };

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      <CCAppBar
        label="Nộp hồ sơ"
        isBack={true}
        iconsAction={[
          {
            icon: 'arrow-right',
            onPress: handleSubmit,
            disabled: !canProceed(),
          },
        ]}
      />
      <StepMenuVertical currentStep={currentStep} options={optionSteps} />

      <KeyboardAwareScrollView
        style={styles.scrollContent}
        contentContainerStyle={styles.scrollContentContainer}
        keyboardShouldPersistTaps="handled">
        <FormProvider {...methods}>
          <View style={styles.content}>
            <Text style={[styles.title, {color: theme.colors.onBackground}]}>
              Chọn vai trò thực hiện
            </Text>
            <Text
              style={[styles.subtitle, {color: theme.colors.onSurfaceVariant}]}>
              Vui lòng chọn một vai trò phù hợp để tiếp tục
            </Text>
            <View style={styles.roleList}>
              {roleOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.roleOption,
                    {
                      borderColor: theme.colors.outline,
                      backgroundColor: theme.colors.surface,
                    },
                    selectedRole === option.value && {
                      borderColor: theme.colors.primary,
                      backgroundColor: theme.colors.primaryContainer,
                      shadowColor: theme.colors.primary,
                      shadowOpacity: 0.15,
                      shadowRadius: 8,
                      elevation: 2,
                    },
                  ]}
                  activeOpacity={0.7}
                  onPress={() => handleRoleSelect(option.value)}>
                  <Avatar.Icon
                    size={isTablet ? 48 : isSmallScreen ? 32 : 36}
                    icon={selectedRole === option.value ? 'check' : option.icon}
                    style={
                      selectedRole === option.value
                        ? [
                            styles.selectedIcon,
                            {backgroundColor: theme.colors.primary},
                          ]
                        : [
                            styles.icon,
                            {backgroundColor: theme.colors.surfaceVariant},
                          ]
                    }
                    color={
                      selectedRole === option.value
                        ? theme.colors.onPrimary
                        : theme.colors.onSurfaceVariant
                    }
                  />
                  <Text
                    style={[
                      styles.radioLabel,
                      {color: theme.colors.onSurface},
                      selectedRole === option.value && {
                        color: theme.colors.onPrimaryContainer,
                        fontWeight: 'bold',
                      },
                    ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </FormProvider>
      </KeyboardAwareScrollView>

      <View
        style={[commonStyle.footer, {backgroundColor: theme.colors.surface}]}>
        <Button
          mode="outlined"
          style={commonStyle.footerButton}
          onPress={() => {
            nav.goBack();
          }}>
          Quay lại
        </Button>
        <Button
          mode="contained"
          style={commonStyle.footerButton}
          disabled={!canProceed()}
          onPress={handleSubmit}>
          Tiếp theo
        </Button>
      </View>

      <Portal>
        <Dialog
          visible={showKhaiHoDialog}
          onDismiss={() => {
            setSelectedRole('');
            handleKhaiHoDialogClose();
          }}
          style={styles.dialog}>
          <FormProvider {...methods}>
            <Dialog.Title style={styles.dialogTitle}>
              Thông tin người uỷ quyền
            </Dialog.Title>
            <Dialog.ScrollArea style={styles.dialogScrollArea}>
              <KeyboardAwareScrollView
                extraScrollHeight={Platform.OS === 'ios' ? 10 : 20}
                enableOnAndroid={true}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={[
                  styles.dialogContent,
                  Platform.OS === 'ios' && {flexGrow: 0},
                ]}>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenHoTen"
                    label="Họ Tên người Uỷ Quyền"
                    placeholder="Nhập họ tên"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFDatePicker
                    name="uyQuyenNgaySinh"
                    label="Ngày tháng năm sinh"
                    placeholder="YYYY-MM-DD"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenSoGiayTo"
                    label="Số giấy tờ"
                    placeholder="Nhập số giấy tờ"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="uyQuyenMoiQuanHe"
                    label="Mối quan hệ với người uỷ quyền"
                    placeholder="Nhập mối quan hệ"
                    required
                  />
                </View>
              </KeyboardAwareScrollView>
            </Dialog.ScrollArea>
            <Dialog.Actions style={styles.dialogActions}>
              <Button
                mode="outlined"
                onPress={() => {
                  setSelectedRole('');
                  handleKhaiHoDialogClose();
                }}
                style={styles.dialogButton}>
                Hủy
              </Button>
              <Button
                mode="contained"
                disabled={!isKhaiHoFormValid}
                onPress={() => {
                  setShowKhaiHoDialog(false);
                  handleSubmit();
                }}
                style={styles.dialogButton}>
                Xác nhận
              </Button>
            </Dialog.Actions>
          </FormProvider>
        </Dialog>
        <Dialog
          visible={showDoanhNghiepDialog}
          onDismiss={() => {
            setSelectedRole('');
            handleDoanhNghiepDialogClose();
          }}
          style={styles.dialog}>
          <FormProvider {...methods}>
            <Dialog.Title style={styles.dialogTitle}>
              Thông tin doanh nghiệp uỷ quyền
            </Dialog.Title>
            <Dialog.ScrollArea style={styles.dialogScrollArea}>
              <KeyboardAwareScrollView
                extraScrollHeight={Platform.OS === 'ios' ? 10 : 20}
                enableOnAndroid={true}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
                contentContainerStyle={[
                  styles.dialogContent,
                  Platform.OS === 'ios' && {flexGrow: 0},
                ]}>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepTen"
                    label="Tên Doanh nghiệp uỷ quyền"
                    placeholder="Nhập tên doanh nghiệp"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepSoDKKD"
                    label="Số giấy chứng nhận đăng ký kinh doanh"
                    placeholder="Nhập số giấy chứng nhận đăng ký kinh doanh"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepMaSoThue"
                    label="Mã số Thuế"
                    placeholder="Nhập mã số thuế"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepDiaChi"
                    label="Địa chỉ trụ sở"
                    placeholder="Nhập địa chỉ trụ sở"
                    required
                  />
                </View>
                <View style={styles.formRow}>
                  <RHFTextField
                    name="doanhNghiepSoDienThoai"
                    label="Số điện thoại"
                    placeholder="Nhập số điện thoại"
                    required
                  />
                </View>
              </KeyboardAwareScrollView>
            </Dialog.ScrollArea>
            <Dialog.Actions style={styles.dialogActions}>
              <Button
                mode="outlined"
                onPress={() => {
                  setSelectedRole('');
                  handleDoanhNghiepDialogClose();
                }}
                style={styles.dialogButton}>
                Hủy
              </Button>
              <Button
                mode="contained"
                disabled={!isDoanhNghiepFormValid}
                onPress={() => {
                  setShowDoanhNghiepDialog(false);
                  handleSubmit();
                }}
                style={styles.dialogButton}>
                Xác nhận
              </Button>
            </Dialog.Actions>
          </FormProvider>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepMenuVertical: {
    marginTop: 4,
    marginBottom: 4,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: FOOTER_HEIGHT + 16, // Add padding to prevent overlap with footer
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: isTablet ? 32 : 16,
    paddingVertical: 20,
  },
  roleList: {
    width: '100%',
    maxWidth: isTablet ? 600 : 400,
    alignSelf: 'center',
    marginTop: 16,
  },
  title: {
    fontSize: isTablet ? 24 : isSmallScreen ? 18 : 20,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: isTablet ? 18 : isSmallScreen ? 14 : 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: isTablet ? 26 : isSmallScreen ? 20 : 24,
  },
  roleOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: isTablet ? 20 : 16,
    paddingVertical: isTablet ? 20 : isSmallScreen ? 12 : 16,
    paddingHorizontal: isTablet ? 24 : isSmallScreen ? 12 : 16,
    borderRadius: 12,
    borderWidth: 1,
    minHeight: isTablet ? 80 : isSmallScreen ? 56 : 64,
  },
  icon: {
    marginRight: isTablet ? 12 : 8,
  },
  selectedIcon: {
    marginRight: isTablet ? 12 : 8,
  },
  radioLabel: {
    fontSize: isTablet ? 18 : isSmallScreen ? 14 : 16,
    marginLeft: isTablet ? 12 : 8,
    fontWeight: '500',
    flex: 1,
  },
  dialog: {
    maxHeight:
      Platform.OS === 'ios' ? screenHeight * 0.75 : screenHeight * 0.85,
    maxWidth: screenWidth > 768 ? 600 : screenWidth - 32,
    width: screenWidth > 768 ? 600 : screenWidth - 32,
    alignSelf: 'center',
    marginHorizontal: 16,
    ...(Platform.OS === 'ios' && {
      marginBottom: 20,
    }),
  },
  dialogTitle: {
    textAlign: 'center',
    fontSize: screenWidth > 768 ? 20 : screenWidth < 375 ? 16 : 18,
    fontWeight: 'bold',
    paddingBottom: 16,
    paddingTop: 8,
  },
  dialogScrollArea: {
    maxHeight:
      Platform.OS === 'ios'
        ? screenWidth > 768
          ? 400
          : screenWidth < 375
          ? 250
          : 300
        : screenWidth > 768
        ? 450
        : screenWidth < 375
        ? 280
        : 350,
    paddingHorizontal: 0,
    flexGrow: 0,
    ...(Platform.OS === 'ios' && {
      paddingBottom: 0,
    }),
  },
  dialogContent: {
    paddingHorizontal: screenWidth > 768 ? 24 : 16,
    paddingVertical: Platform.OS === 'ios' ? 4 : 8,
    flexGrow: 1,
    ...(Platform.OS === 'ios' && {
      paddingBottom: 0,
    }),
  },
  dialogActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 16,
    paddingTop: Platform.OS === 'ios' ? 12 : 16,
    paddingBottom: Platform.OS === 'ios' ? 4 : 8,
    paddingHorizontal: screenWidth > 768 ? 24 : 16,
    ...(Platform.OS === 'ios' && {
      marginTop: 0,
    }),
  },
  dialogButton: {
    flex: 1,
  },
  formRow: {
    marginBottom: Platform.OS === 'ios' ? 12 : 16,
  },
});

export default StepRoleChoose;
