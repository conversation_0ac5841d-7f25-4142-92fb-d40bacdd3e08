import React, {useRef} from 'react';
import {FlatList, StyleSheet} from 'react-native';
import TemplateItem from './TemplateItem';
import EmptyState from '../../DrawNumber/components/EmptyState';

// Template interface
interface Template {
  id: string;
  name: string;
}

interface TemplateListProps {
  templates: Template[];
  selectedTemplate: Template | null;
  hasReturned: boolean;
  onSelectTemplate: (template: Template) => void;
  searchQuery: string;
}

const TemplateList: React.FC<TemplateListProps> = ({
  templates,
  selectedTemplate,
  hasReturned,
  onSelectTemplate,
  searchQuery,
}) => {
  // Reference for FlatList to enable scrolling to selected item
  const templateListRef = useRef<FlatList>(null);

  const renderEmptyComponent = () => (
    <EmptyState
      searchQuery={searchQuery}
      customMessage={
        searchQuery ? 'Không tìm thấy thủ tục phù hợp' : 'Không có thủ tục nào'
      }
    />
  );

  return (
    <FlatList
      ref={templateListRef}
      data={templates}
      renderItem={({item}) => (
        <TemplateItem
          template={item}
          hasReturned={hasReturned}
          onSelect={onSelectTemplate}
        />
      )}
      keyExtractor={item => item.id}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={renderEmptyComponent()}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingBottom: 12,
    paddingHorizontal: 1,
  },
});

export default TemplateList;
