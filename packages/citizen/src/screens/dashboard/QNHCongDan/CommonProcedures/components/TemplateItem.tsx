import React from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../../../styles/QNH_textStyle';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {getIconAndPaintForName} from '../../../../../components/QNH_CongDanComponents/CommonTemplate/ColorPickerIconMap';

// Template interface
interface Template {
  id: string;
  name: string;
}

interface TemplateItemProps {
  template: Template;
  isSelected: boolean;
  hasReturned: boolean;
  onSelect: (template: Template) => void;
}

const TemplateItem: React.FC<TemplateItemProps> = ({
  template,
  isSelected,
  hasReturned: _hasReturned,
  onSelect,
}) => {
  const theme = useTheme();
  const iconConfig = getIconAndPaintForName(template.name);

  const handlePress = () => {
    onSelect(template);
  };

  return (
    <Pressable
      style={({pressed}) => [
        styles.card,
        {
          backgroundColor: theme.colors.surface,
          borderColor: isSelected ? theme.colors.primary : theme.colors.outline,
          borderWidth: isSelected ? 2 : 1,
        },
        styles.horizontal,
        pressed && {
          backgroundColor: theme.colors.elevation?.level2 || '#f0f0f0',
        },
      ]}
      onPress={handlePress}
      accessible
      accessibilityRole="button">
      <View
        style={[
          styles.iconContainer,
          {
            backgroundColor: iconConfig.paint[1],
            borderRadius: 8,
            width: 40,
            height: 40,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
          },
        ]}>
        <Icon size={28} name={iconConfig.icon} color={iconConfig.paint[0]} />
      </View>
      <View style={styles.textContainer}>
        <Text
          numberOfLines={2}
          ellipsizeMode="tail"
          className={textStyles.subTitle2}
          style={{
            color: theme.colors.onSurface,
            textAlign: 'left',
            flexShrink: 1,
            flex: 1,
            fontWeight: '600',
          }}>
          {template.name}
        </Text>
      </View>
      {isSelected && (
        <View style={styles.selectedIndicator}>
          <Icon name="check-circle" size={24} color={theme.colors.primary} />
        </View>
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  horizontal: {
    width: '100%',
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  iconContainer: {
    // backgroundColor, borderRadius, width, height, alignItems, justifyContent are set inline
  },
  textContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  selectedIndicator: {
    marginLeft: 8,
  },
});

export default TemplateItem;
