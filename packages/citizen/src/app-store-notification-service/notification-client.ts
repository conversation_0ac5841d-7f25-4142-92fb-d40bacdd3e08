import {ApiClient, useAuthStore} from '@ac-mobile/common';
import {Platform} from 'react-native';
import RNRestart from 'react-native-restart';
import {APP_STORE_PUSH_NOTI} from '../api/api-config';

const NotificationServiceClient = ApiClient.getInstance(
  'app-store-notification-service',
);

NotificationServiceClient.setSelectRefreshing(() => {
  return useAuthStore.getState().isRefreshing;
}).setOnError(async (status: number) => {
  if (status === 401) {
    try {
      useAuthStore.getState().logout();
    } catch (error) {
      if (Platform.OS !== 'ios') {
        RNRestart.restart();
      }
    }
  }
});

NotificationServiceClient.setEndpoint(
  `${APP_STORE_PUSH_NOTI}/app-store-notification/api`,
);

const NotificationApi = NotificationServiceClient.api;

export {NotificationServiceClient, NotificationApi};
