// HN
import {ApiClient, useAuthStore} from '@ac-mobile/common';
import {DVC_ENDPOINT} from '../api-config';

export const AcDVCHNClient = ApiClient.getInstance('ac-dvc-hn-client')
  .setEndpoint(`${DVC_ENDPOINT}/api`)
  .setSelectAccessToken(() => {
    return '';
  })
  .setSelectRefreshing(() => {
    return '';
  })
  .setOnError((status: number) => {
    if (status === 401) {
      console.log('Hết phiên đăng nhập, vui lòng đăng nhập lại');
      useAuthStore.getState().logout();
    } else {
      // TODO:
    }
  });
