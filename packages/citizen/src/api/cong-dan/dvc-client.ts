// HN
import {ApiClient, useAuthStore} from '@ac-mobile/common';
import {API_CONFIG, ENDPOINT} from '../api-config';

export const AcDVCClient = ApiClient.getInstance('ac-app-dashboard')
  .setEndpoint(ENDPOINT)
  .setSelectAccessToken(() => {
    return useAuthStore.getState().accessToken;
  })
  .setOnError((status: number) => {
    if (status === 401) {
      console.log('Hết phiên đăng nhập, vui lòng đăng nhập lại');
      useAuthStore.getState().logout();
    } else {
      // TODO:
    }
  })
  .setSelectRefreshing(() => {
    return useAuthStore.getState().isRefreshing;
  })
  .setRefreshingAccessToken(async () => {
    try {
      const refreshToken = useAuthStore.getState().refreshToken;
      if (!refreshToken) {
        useAuthStore.getState().logout();
      }
      useAuthStore.getState().setRefreshing(true);
      const user = useAuthStore.getState().user;
      if (refreshToken) {
        const resRefreshToken = await fetch(
          `${API_CONFIG.ENDPOINT}/User/RefreshToken?refreshToken=${refreshToken}&userID=${user?.user_PortalID}`,
        );
        if (resRefreshToken.ok) {
          const data = await resRefreshToken.json();
          useAuthStore.getState().setAccessToken(data.data.accessToken);
          useAuthStore.getState().setRefreshToken(data.data.refreshToken);
        } else {
          throw new Error('Refresh token failed');
        }
      }
    } catch (error) {
      console.log(error, 'error refresh token');
      useAuthStore.getState().logout();
    } finally {
      useAuthStore.getState().setRefreshing(false);
    }

    // TODO: refresh token when api available. Currently we logout when token expires
  })
  .setSelectRefreshToken(() => {
    return useAuthStore.getState().refreshToken;
  });
