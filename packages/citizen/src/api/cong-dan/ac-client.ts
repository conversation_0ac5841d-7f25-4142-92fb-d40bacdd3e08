import {ApiClient, useConfigStore} from '@ac-mobile/common';

export const AcRequesterClient = ApiClient.getInstance('ac-app-requester-biz')
  .setEndpoint(
    'https://dl-requester-biz-api.dev.cluster02.fis-cloud.xplat.online/requester-biz/api',
  )
  .setSelectAccessToken(async () => {
    const loginInfo = await useConfigStore.getState().getConfig('loginInfo');
    // console.log('loginInfo', loginInfo?.access_token);
    return loginInfo?.access_token;
  })
  .setSelectRefreshToken(() => {
    const loginInfo = useConfigStore.getState().getConfig('loginInfo');
    return loginInfo?.refresh_token || '';
  })
  .setOnError((status: number) => {
    if (status === 401) {
      console.log('Hết phiên đăng nhập, vui lòng đăng nhập lại');
    } else {
      // TODO:
      console.log('Error occurred with status:', status);
    }
  });
