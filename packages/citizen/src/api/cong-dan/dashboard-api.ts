import {AnyObj} from '@ac-mobile/common';
import {AcRequesterClient} from './ac-client';
import {AcDVCHNClient} from './dvc-hn-client';

export const headersDefault = {
  // 'X-Requester-Service-Id': 'b1552792-8271-4f60-90de-2ebce42be82f',
  'X-Requester-Service-Id': 'b1552792-8271-4f60-90de-2ebce42be82f',
  'X-Requester-Service-Key-Auth-Profile': 'citizen',
};

export const getTemplates = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers?: AnyObj;
}) => {
  return AcRequesterClient.api.get('/v1/templates', {
    params,
    headers: {...headers, ...headersDefault},
  });
};

export const getTemplate = ({
  templateId,
  headers,
}: {
  templateId: string;
  headers?: AnyObj;
}) => {
  return AcRequesterClient.api.get('/v1/templates/' + templateId, {
    headers,
  });
};

export const getShares = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers: AnyObj;
}) => {
  return AcRequesterClient.api.get('/v1/shares', {
    params,
    headers,
  });
};

const getDonViByTTHCID = (params: AnyObj) => {
  try {
    return AcDVCHNClient.api.get('/Public/DVC_DONVI_BY_TTHC', {
      params,
    });
  } catch (error) {
    console.log('error At GetSign', JSON.stringify(error));
    return undefined;
  }
};

const getListBranch = ({params, headers}: {params: AnyObj; headers: AnyObj}) =>
  AcRequesterClient.api.get('/v1/draw-number/list-branch', {
    params,
    headers,
  });

const getListService = ({params, headers}: {params: AnyObj; headers: AnyObj}) =>
  AcRequesterClient.api.get('/v1/draw-number/list-branch', {
    params,
    headers,
  });

const drawNumber = ({data, headers}: {data: AnyObj; headers: AnyObj}) =>
  AcRequesterClient.api.post('/v1/draw-number', data, {
    headers,
  });

const getListTicketByIdentification = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers: AnyObj;
}) =>
  AcRequesterClient.api.get(
    '/v1/draw-number/list-tickets-by-identification-id',
    {
      params,
      headers,
    },
  );

const getTicketDetail = ({
  id,
  params,
  headers,
}: {
  id: string;
  params: AnyObj;
  headers: AnyObj;
}) =>
  AcRequesterClient.api.get(`/v1/draw-number/get-ticket-detail/${id}`, {
    params,
    headers,
  });

const cancelDrawNumber = ({data, headers}: {data: AnyObj; headers: AnyObj}) =>
  AcRequesterClient.api.post('/v1/draw-number', data, {
    headers,
  });

const initUserForm = ({data, headers}: {data: AnyObj; headers?: AnyObj}) =>
  AcRequesterClient.api.post('v1/document-sets/method/user-form', data, {
    headers: {...headersDefault, ...headers},
  });

const downloadPdf = ({data, headers}: {data: AnyObj; headers?: AnyObj}) =>
  AcRequesterClient.api.post(
    'v1/document-sets/method/print/html-to-pdf',
    data,
    {
      headers: {...headersDefault, ...headers},
    },
  );

export const handleFunction = {
  getTemplates,
  getShares,
  getTemplate,
  getDonViByTTHCID,
};

export const drawNumberFunction = {
  getListBranch,
  getListService,
  drawNumber,
  getListTicketByIdentification,
  getTicketDetail,
  cancelDrawNumber,
};

export const eFormFunction = {
  initUserForm,
  downloadPdf,
};
