import {AnyObj} from '@ac-mobile/common';
import {AcRequesterClient} from './cong-dan/ac-client';
import {AcDVCClient} from './cong-dan/dvc-client';

const defaultHeaders = {
  // 'X-Requester-Service-Id': 'b1552792-8271-4f60-90de-2ebce42be82f',
  'X-Requester-Service-Id': 'b1552792-8271-4f60-90de-2ebce42be82f',
  'X-Requester-Service-Key-Auth-Profile': 'citizen',
};

export const getTemplates = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers?: AnyObj;
}) => {
  return AcRequesterClient.api.get('/v1/templates', {
    params,
    headers: {...defaultHeaders, ...headers},
  });
};

export const getTemplate = ({
  templateId,
  headers,
}: {
  templateId: string;
  headers: AnyObj;
}) =>
  AcRequesterClient.api.get('/v1/templates/' + templateId, {
    headers: {...defaultHeaders, ...headers},
  });

export const getShares = ({
  params,
  headers,
}: {
  params: AnyObj;
  headers: AnyObj;
}) => {
  return AcRequesterClient.api.get('/v1/shares', {
    params,
    headers: {...defaultHeaders, ...headers},
  });
};

const getDonViByTTHCID = (params: AnyObj) => {
  try {
    return AcDVCClient.api.get('/api/Public/DVC_DONVI_BY_TTHC', {
      params,
    });
  } catch (error) {
    console.log('error At GetSign', JSON.stringify(error));
    return undefined;
  }
};

export const handleFunction = {
  getTemplates,
  getShares,
  getTemplate,
  getDonViByTTHCID,
};
