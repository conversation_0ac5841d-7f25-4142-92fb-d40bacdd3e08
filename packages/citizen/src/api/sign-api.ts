import {AcSignClient} from './ac-sign-client';
type SignProfile = {
  simId: string;
  signerCertificate: string;
  reason: string;
  textContent: string;
  visibleSignature: {
    offset: string;
    boxSize: string;
    requiredText: string;
    imageData?: string;
    page: number;
  };
  checkText: boolean;
  checkMark: boolean;
};

export type DocumentSignRequest = {
  signProfile: SignProfile;
  fileName: string;
  fileUrl: string;
};

export const simPKi = async (data: DocumentSignRequest) => {
  try {
    const rs = await AcSignClient.post('/api/v1/simpki/sign-url', data, {
      responseType: 'blob',
    });
    return rs;
  } catch (error) {
    console.log(error, 'error AT simPKi');
  }
};
export const simPKiFile = async (data: any) => {
  try {
    const formData = new FormData();

    const {
      signProfile: {
        simId,
        signerCertificate,
        reason,
        textContent,
        visibleSignature,
        checkMark,
        checkText,
      },
      file: {uri, type, name},
    } = data;

    if (simId) {
      formData.append('SimID', simId.toString());
    }
    if (signerCertificate) {
      formData.append('SignerCertificate', signerCertificate.toString());
    }
    if (reason) {
      formData.append('Reason', reason.toString());
    }
    if (textContent) {
      formData.append('TextContent', textContent.toString());
    }
    if (visibleSignature.offset) {
      formData.append('VisibleSignature.Offset', visibleSignature.offset);
    }
    if (visibleSignature.boxSize) {
      formData.append(
        'VisibleSignature.BoxSize',
        visibleSignature.boxSize.toString(),
      );
    }
    if (visibleSignature.requiredText) {
      formData.append(
        'VisibleSignature.RequiredText',
        visibleSignature.requiredText.toString(),
      );
    }
    if (visibleSignature.imageData) {
      formData.append('VisibleSignature.ImageData', visibleSignature.imageData);
    }
    if (visibleSignature.page) {
      formData.append(
        'VisibleSignature.Page',
        visibleSignature.page.toString(),
      );
    }
    if (checkMark) {
      formData.append('CheckMark', checkMark.toString());
    }
    if (checkText) {
      formData.append('CheckText', checkText.toString());
    }
    const formFile = {
      uri: uri,
      type: type || 'application/pdf',
      name: name,
    };
    formData.append('FormFile', formFile);

    const rs = await AcSignClient.post(
      '/remote-sign/api/v1/simpki/sign-file',
      formData,
      {
        responseType: 'blob',
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: data => data,
      },
    );
    console.log(rs, 'rs');

    return rs;
  } catch (error) {
    console.log(error, 'error AT simPKxxxxi');
  }
};

export const getCert = async (params: {simid: string}) => {
  try {
    return await AcSignClient.get('/remote-sign/api/v1/simpki/get-cert', {
      params: {
        ...params,
      },
    });
  } catch (error: any) {
    console.log(error.response, 'error AT getCert');
    throw error;
  }
};
