import {useEffect, type ReactNode} from 'react';

import SplashScreen from '../SplashScreen';
import {useAuthStore} from '@ac-mobile/common';
import {useGlobalStore} from '../../stores';

export const GlobalStateView = ({children}: {children: ReactNode}) => {
  const {loading, initialized, updateState} = useGlobalStore();
  const {user, logout} = useAuthStore();

  useEffect(() => {
    updateState();
  }, []);

  if (initialized) {
    return children;
  }

  return <SplashScreen />;
};
