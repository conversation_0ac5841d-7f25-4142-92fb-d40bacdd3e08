type AcExtendColors = {
  action: {
    disabled: string;
    disabledBackground: string;
    focus: string;
    hover: string;
    selected: string;
  };
  background: {
    default: string;
    paper: string;
    neutral: string;
    contrast: string;
  };
  common: {
    black: {
      black: string;
      blackTrans12: string;
      blackTrans16: string;
      blackTrans24: string;
      blackTrans32: string;
      blackTrans48: string;
      blackTrans64: string;
      blackTrans8: string;
      blackTrans80: string;
    };
    white: {
      white: string;
      whiteTrans12: string;
      whiteTrans16: string;
      whiteTrans24: string;
      whiteTrans32: string;
      whiteTrans48: string;
      whiteTrans64: string;
      whiteTrans8: string;
      whiteTrans80: string;
    };
  };
  component: {
    backdrop: string;
    button: {
      default: {
        backgroundHover: string;
        text: string;
        background: string;
      };
      inherit: {
        text: string;
        background: string;
        backgroundHover: string;
      };
      outlined: string;
    };
    divider: string;
    input: {
      outlined: string;
      underline: string;
    };
    paper: {
      blur: string;
      blurBold: string;
      outlined: string;
    };
    snackbar: {
      background: string;
      text: string;
    };
    tooltip: string;
  };
  error: {
    default: {
      contrastText: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
  grey: {
    default: {
      grey0: string;
      grey1: string;
      grey2: string;
      grey3: string;
      grey4: string;
      grey5: string;
      grey6: string;
      grey7: string;
      grey8: string;
      grey9: string;
    };
    transparent: {
      greyTrans12: string;
      greyTrans16: string;
      greyTrans24: string;
      greyTrans32: string;
      greyTrans48: string;
      greyTrans8: string;
    };
  };
  info: {
    default: {
      contrastText: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
  primary: {
    default: {
      contrastText: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
  secondary: {
    default: {
      contrastText: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
  success: {
    default: {
      contrastText: string;
      color: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  warning: {
    default: {
      contrastText: string;
      dark: string;
      darker: string;
      light: string;
      lighter: string;
      main: string;
    };
    transparent: {
      trans12: string;
      trans16: string;
      trans24: string;
      trans32: string;
      trans48: string;
      trans8: string;
    };
  };
};

export type {AcExtendColors};
