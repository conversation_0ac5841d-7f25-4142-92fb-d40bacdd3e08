import React, {useContext, useCallback, useMemo, useState} from 'react';
import {
  NavigationContainer,
  type LinkingOptions,
  DarkTheme as NavigationDarkTheme,
  DefaultTheme as NavigationDefaultTheme,
} from '@react-navigation/native';
import {
  MD3DarkTheme,
  MD3LightTheme,
  PaperProvider,
  adaptNavigationTheme,
} from 'react-native-paper';
import deepMerge from 'deepmerge';
import type {AcExtendColors} from './ThemeTypes';
import {acDarkColors} from './ac-dark-color';
import {acLightColors} from './ac-light-color';
import {IObject} from '../../stores/dataType';

const {LightTheme, DarkTheme} = adaptNavigationTheme({
  reactNavigationLight: NavigationDefaultTheme,
  reactNavigationDark: NavigationDarkTheme,
});

const CombinedLightTheme = deepMerge(MD3LightTheme, LightTheme);
const CombinedDarkTheme = deepMerge(MD3DarkTheme, DarkTheme);

export type BaseTheme = typeof CombinedLightTheme | typeof CombinedDarkTheme;

export const initAcExtendColor: AcExtendColors = {} as any;

export type AcTheme = BaseTheme & {
  ac: AcExtendColors;
};

type ThemeContextProviderProps = {
  children: ({theme}: {theme: AcTheme}) => React.ReactNode;
  overrideThemeFn?: (theme: BaseTheme, dm: typeof deepMerge) => IObject;
  /**
   * Hàm chuyển trạng thai sau khi ứng dụng được Load
   *
   * @example
   *
   * const onReady = () => RNBootSplash.hide({ fade: true, duration: 500 });
   *
   * @returns
   */
  onReady: () => any;
  linking?: LinkingOptions<ReactNavigation.RootParamList>;
};

const ThemeContextProvider = ({
  children,
  onReady,
  linking,
  overrideThemeFn,
}: ThemeContextProviderProps) => {
  const [isThemeDark, setIsThemeDark] = useState(false);
  const [useRippleEffect, setUseRippleEffect] = useState(true);
  // const themeBase = isThemeDark ? CombinedDarkTheme : CombinedDefaultTheme;

  const theme: AcTheme = useMemo(() => {
    // console.log(JSON.stringify(mdTheme.light, null, 2), 'llll');
    // console.log(JSON.stringify(mdTheme.dark, null, 2), 'dark');
    // return isThemeDark
    //   ? {
    //       ...MD3DarkTheme,
    //       colors: mdTheme.dark,
    //       ac: deepMerge(isThemeDark ? acDarkColors : acLightColors, {}),
    //     }
    //   : {
    //       ...MD3LightTheme,
    //       colors: mdTheme.light,
    //       ac: deepMerge(isThemeDark ? acDarkColors : acLightColors, {}),
    //     };

    return {
      ...(isThemeDark ? CombinedDarkTheme : CombinedLightTheme),
      ac: deepMerge(isThemeDark ? acDarkColors : acLightColors, {}),
    };
  }, [isThemeDark]);

  const toggleTheme = useCallback(() => {
    return setIsThemeDark(!isThemeDark);
  }, [isThemeDark]);

  const toggleUseRippleEffect = useCallback(() => {
    return setUseRippleEffect(!useRippleEffect);
  }, [useRippleEffect]);

  const preferences = useMemo(
    () => ({
      isThemeDark,
      toggleTheme,
      toggleUseRippleEffect,
      useRippleEffect,
      theme,
    }),
    [
      //
      theme,
      isThemeDark,
      toggleTheme,
      toggleUseRippleEffect,
      useRippleEffect,
    ],
  );

  const mTheme: AcTheme = {
    ...theme,
    fonts: {
      bodyLarge: {
        // body1
        fontFamily: theme.fonts.bodyLarge.fontFamily,
        fontSize: 16,
        fontWeight: '400',
        letterSpacing: 0,
        lineHeight: 24,
      },
      bodyMedium: {
        // body2
        fontFamily: theme.fonts.bodyMedium.fontFamily,
        fontSize: 14,
        fontWeight: '400',
        letterSpacing: 0,
        lineHeight: 22,
      },
      bodySmall: {
        // caption
        fontFamily: theme.fonts.bodySmall.fontFamily,
        fontSize: 12,
        fontWeight: '400',
        letterSpacing: 0,
        lineHeight: 18,
      },
      default: {
        fontFamily: theme.fonts.default.fontFamily,
        fontWeight: '400',
        letterSpacing: 0,
      },
      displayLarge: {
        // h1
        fontFamily: theme.fonts.displayLarge.fontFamily,
        fontSize: 64,
        fontWeight: '800',
        letterSpacing: 0,
        lineHeight: 80,
      },
      displayMedium: {
        // h2
        fontFamily: theme.fonts.displayMedium.fontFamily,
        fontSize: 48,
        fontWeight: '800',
        letterSpacing: 0,
        lineHeight: 64,
      },
      displaySmall: {
        // h3
        fontFamily: theme.fonts.displaySmall.fontFamily,
        fontSize: 32,
        fontWeight: '700',
        letterSpacing: 0,
        lineHeight: 48,
      },
      headlineLarge: {
        // h4
        fontFamily: theme.fonts.headlineLarge.fontFamily,
        fontSize: 24,
        fontWeight: '700',
        letterSpacing: 0,
        lineHeight: 36,
      },
      headlineMedium: {
        // h5
        fontFamily: theme.fonts.headlineMedium.fontFamily,
        fontSize: 20,
        fontWeight: '700',
        letterSpacing: 0,
        lineHeight: 30,
      },
      headlineSmall: {
        // h6
        fontFamily: theme.fonts.headlineSmall.fontFamily,
        fontSize: 18,
        fontWeight: '700',
        letterSpacing: 0,
        lineHeight: 28,
      },
      labelLarge: {
        fontFamily: theme.fonts.labelLarge.fontFamily,
        fontSize: 14,
        fontWeight: '500',
        letterSpacing: 0.1,
        lineHeight: 20,
      },
      labelMedium: {
        fontFamily: theme.fonts.labelMedium.fontFamily,
        fontSize: 12,
        fontWeight: '500',
        letterSpacing: 0.5,
        lineHeight: 16,
      },
      labelSmall: {
        fontFamily: theme.fonts.labelSmall.fontFamily,
        fontSize: 11,
        fontWeight: '500',
        letterSpacing: 0.5,
        lineHeight: 16,
      },
      titleLarge: {
        // subtitle1
        fontFamily: theme.fonts.titleLarge.fontFamily,
        fontSize: 16,
        fontWeight: '600',
        letterSpacing: 0,
        lineHeight: 24,
      },
      titleMedium: {
        // subtitle1
        fontFamily: theme.fonts.titleMedium.fontFamily,
        fontSize: 14,
        fontWeight: '600',
        letterSpacing: 0,
        lineHeight: 22,
      },
      titleSmall: {
        fontFamily: theme.fonts.titleSmall.fontFamily,
        fontSize: 14,
        fontWeight: '500',
        letterSpacing: 0,
        lineHeight: 20,
      },
    },
    ...(overrideThemeFn ? overrideThemeFn(theme, deepMerge) : {}),
  };

  return (
    <ThemeContext.Provider value={preferences}>
      <PaperProvider
        settings={{rippleEffectEnabled: useRippleEffect}}
        theme={mTheme}>
        <NavigationContainer
          linking={linking}
          theme={mTheme}
          onReady={() => onReady()}>
          {children({theme: mTheme})}
        </NavigationContainer>
      </PaperProvider>
    </ThemeContext.Provider>
  );
};

type IThemeContext = {
  toggleTheme: () => void;
  toggleUseRippleEffect: () => void;
  isThemeDark: boolean;
  useRippleEffect: boolean;
  theme: AcTheme;
};

const init: IThemeContext = {
  toggleTheme: () => {},
  toggleUseRippleEffect: () => {},
  isThemeDark: false,
  useRippleEffect: true,
  theme: {} as AcTheme,
};

const ThemeContext = React.createContext<IThemeContext>(init);

function useAcTheme() {
  const {
    isThemeDark,
    theme,
    toggleTheme,
    toggleUseRippleEffect,
    useRippleEffect,
  } = useContext<IThemeContext>(ThemeContext);

  return {
    theme,
    isThemeDark,
    toggleTheme,
    toggleUseRippleEffect,
    useRippleEffect,
  };
}

export {
  //
  ThemeContext,
  useAcTheme,
  ThemeContextProvider,
};
