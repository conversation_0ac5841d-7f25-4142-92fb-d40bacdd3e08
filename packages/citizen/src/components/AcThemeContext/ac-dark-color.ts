import {AcExtendColors} from './ThemeTypes';

const acDarkColors: AcExtendColors = {
  action: {
    disabled: '#637381cc',
    disabledBackground: '#6373813d',
    focus: '#6373813d',
    hover: '#63738114',
    selected: '#63738129',
  },
  background: {
    default: '#161C24',
    paper: '#ffffff',
    neutral: '#f4f6f8',
    contrast: '#f9fafb',
  },
  common: {
    black: {
      black: '#ffffffff',
      blackTrans12: '#ffffff1f',
      blackTrans16: '#ffffff29',
      blackTrans24: '#ffffff3d',
      blackTrans32: '#ffffff52',
      blackTrans48: '#ffffff7a',
      blackTrans64: '#ffffffa3',
      blackTrans8: '#ffffff14',
      blackTrans80: '#ffffffcc',
    },
    white: {
      white: '#000000ff',
      whiteTrans12: '#0000001f',
      whiteTrans16: '#00000029',
      whiteTrans24: '#0000003d',
      whiteTrans32: '#00000052',
      whiteTrans48: '#0000007a',
      whiteTrans64: '#000000a3',
      whiteTrans8: '#00000014',
      whiteTrans80: '#000000cc',
    },
  },
  component: {
    backdrop: '#161c24cc',
    button: {
      default: {
        backgroundHover: '#c4cdd5',
        text: '#212b36',
        background: '#dfe3e8',
      },
      inherit: {
        text: '#ffffff',
        background: '#212b36',
        backgroundHover: '#454f5b',
      },
      outlined: '#ffffff3d',
    },
    divider: '#919eab33',
    input: {
      outlined: '#63738133',
      underline: '#63738152',
    },
    paper: {
      blur: '#212b36e6',
      blurBold: '#212b36e6',
      outlined: '#63738129',
    },
    snackbar: {
      background: '#ffffffff',
      text: '#212b36ff',
    },
    tooltip: '#ffffffff',
  },
  error: {
    default: {
      contrastText: '#ffffffff',
      dark: '#ffac82ff',
      darker: '#ffe9d5ff',
      light: '#b71d18ff',
      lighter: '#7a0916ff',
      main: '#ff5630ff',
    },
    transparent: {
      trans12: '#ff56301f',
      trans16: '#ff563029',
      trans24: '#ff56303d',
      trans32: '#ff563052',
      trans48: '#ff56307a',
      trans8: '#ff563014',
    },
  },
  grey: {
    default: {
      grey0: '#161c24ff',
      grey1: '#1a2129ff',
      grey2: '#919eab1f',
      grey3: '#212b36ff',
      grey4: '#454f5bff',
      grey5: '#637381ff',
      grey6: '#919eabff',
      grey7: '#c4cdd5ff',
      grey8: '#ffffffff',
      grey9: '#ffffffff',
    },
    transparent: {
      greyTrans12: '#6373811f',
      greyTrans16: '#63738129',
      greyTrans24: '#6373813d',
      greyTrans32: '#63738152',
      greyTrans48: '#6373817a',
      greyTrans8: '#63738114',
    },
  },
  info: {
    default: {
      contrastText: '#ffffffff',
      dark: '#8db6fcff',
      darker: '#ededfdff',
      light: '#1f40adff',
      lighter: '#0a1f70ff',
      main: '#366ae2ff',
    },
    transparent: {
      trans12: '#366ae21f',
      trans16: '#366ae229',
      trans24: '#366ae23d',
      trans32: '#366ae252',
      trans48: '#366ae27a',
      trans8: '#366ae214',
    },
  },
  primary: {
    default: {
      contrastText: '#ffffffff',
      dark: '#ff9e99ff',
      darker: '#ffedebff',
      light: '#b30c1aff',
      lighter: '#66000fff',
      main: '#d81921ff',
    },
    transparent: {
      trans12: '#d819211f',
      trans16: '#d8192129',
      trans24: '#d819213d',
      trans32: '#d8192152',
      trans48: '#d819217a',
      trans8: '#d8192114',
    },
  },
  secondary: {
    default: {
      contrastText: '#212b36ff',
      dark: '#f7e5c3ff',
      darker: '#fcf8e6ff',
      light: '#c4a272ff',
      lighter: '#785a3aff',
      main: '#eac993ff',
    },
    transparent: {
      trans12: '#eac9931f',
      trans16: '#eac99329',
      trans24: '#eac9933d',
      trans32: '#eac99352',
      trans48: '#eac9937a',
      trans8: '#eac99314',
    },
  },
  success: {
    default: {
      contrastText: '#212b36ff',
      color: '#ffffffff',
      dark: '#77ed8bff',
      darker: '#e1ffe0ff',
      light: '#118d57ff',
      lighter: '#065e49ff',
      main: '#22c55eff',
    },
    transparent: {
      trans12: '#22c55e1f',
      trans16: '#22c55e29',
      trans24: '#22c55e3d',
      trans32: '#22c55e52',
      trans48: '#22c55e7a',
      trans8: '#22c55e14',
    },
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#919EAB',
    disabled: '#637381',
  },
  warning: {
    default: {
      contrastText: '#212b36ff',
      dark: '#ffd666ff',
      darker: '#fff5ccff',
      light: '#b76e00ff',
      lighter: '#7a4100ff',
      main: '#ffab00ff',
    },
    transparent: {
      trans12: '#ffab001f',
      trans16: '#ffab0029',
      trans24: '#ffab003d',
      trans32: '#ffab0052',
      trans48: '#ffab007a',
      trans8: '#ffab0014',
    },
  },
};

export {acDarkColors};
