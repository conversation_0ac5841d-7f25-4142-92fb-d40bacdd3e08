import type { AcExtendColors } from './ThemeTypes';

const acLightColors: AcExtendColors = {
  action: {
    disabled: '#919eabcc',
    disabledBackground: '#919eab3d',
    focus: '#919eab3d',
    hover: '#919eab14',
    selected: '#919eab29',
  },
  background: {
    default: '#ffffff',
    paper: '#ffffff',
    neutral: '#f4f6f8',
    contrast: '#f9fafb',
  },
  common: {
    black: {
      black: '#000000ff',
      blackTrans12: '#0000001f',
      blackTrans16: '#00000029',
      blackTrans24: '#0000003d',
      blackTrans32: '#00000052',
      blackTrans48: '#0000007a',
      blackTrans64: '#000000a3',
      blackTrans8: '#00000014',
      blackTrans80: '#000000cc',
    },
    white: {
      white: '#ffffffff',
      whiteTrans12: '#ffffff1f',
      whiteTrans16: '#ffffff29',
      whiteTrans24: '#ffffff3d',
      whiteTrans32: '#ffffff52',
      whiteTrans48: '#ffffff7a',
      whiteTrans64: '#ffffffa3',
      whiteTrans8: '#ffffff14',
      whiteTrans80: '#ffffffcc',
    },
  },
  component: {
    backdrop: '#161c24cc',
    button: {
      default: {
        backgroundHover: '#c4cdd5',
        text: '#212b36',
        background: '#dfe3e8',
      },
      inherit: {
        text: '#ffffff',
        background: '#212b36',
        backgroundHover: '#454f5b',
      },
      outlined: '#919eab3d',
    },
    divider: '#919eab33',
    input: {
      outlined: '#919eab33',
      underline: '#919eab52',
    },
    paper: {
      blur: '#ffffffe6',
      blurBold: '#ffffffe6',
      outlined: '#919eab29',
    },
    snackbar: {
      background: '#212b36ff',
      text: '#ffffffff',
    },
    tooltip: '#212b36ff',
  },
  error: {
    default: {
      contrastText: '#ffffffff',
      dark: '#b71d18ff',
      darker: '#7a0916ff',
      light: '#ffac82ff',
      lighter: '#ffe9d5ff',
      main: '#ff5630ff',
    },
    transparent: {
      trans12: '#ff56301f',
      trans16: '#ff563029',
      trans24: '#ff56303d',
      trans32: '#ff563052',
      trans48: '#ff56307a',
      trans8: '#ff563014',
    },
  },
  grey: {
    default: {
      grey0: '#ffffffff',
      grey1: '#f9fafbff',
      grey2: '#f4f6f8ff',
      grey3: '#dfe3e8ff',
      grey4: '#c4cdd5ff',
      grey5: '#919eabff',
      grey6: '#637381ff',
      grey7: '#454f5bff',
      grey8: '#212b36ff',
      grey9: '#161c24ff',
    },
    transparent: {
      greyTrans12: '#919eab1f',
      greyTrans16: '#919eab29',
      greyTrans24: '#919eab3d',
      greyTrans32: '#919eab52',
      greyTrans48: '#919eab7a',
      greyTrans8: '#919eab14',
    },
  },
  info: {
    default: {
      contrastText: '#ffffffff',
      dark: '#1f40adff',
      darker: '#0a1f70ff',
      light: '#8db6fcff',
      lighter: '#ededfdff',
      main: '#366ae2ff',
    },
    transparent: {
      trans12: '#366ae21f',
      trans16: '#366ae229',
      trans24: '#366ae23d',
      trans32: '#366ae252',
      trans48: '#366ae27a',
      trans8: '#366ae214',
    },
  },
  primary: {
    default: {
      contrastText: '#ffffffff',
      dark: '#b30c1aff',
      darker: '#66000fff',
      light: '#ff9e99ff',
      lighter: '#ffedebff',
      main: '#d81921ff',
    },
    transparent: {
      trans12: '#d819211f',
      trans16: '#d8192129',
      trans24: '#d819213d',
      trans32: '#d8192152',
      trans48: '#d819217a',
      trans8: '#d8192114',
    },
  },
  secondary: {
    default: {
      contrastText: '#212b36ff',
      dark: '#c4a272ff',
      darker: '#785a3aff',
      light: '#f7e5c3ff',
      lighter: '#fcf8e6ff',
      main: '#eac993ff',
    },
    transparent: {
      trans12: '#eac9931f',
      trans16: '#eac99329',
      trans24: '#eac9933d',
      trans32: '#eac99352',
      trans48: '#eac9937a',
      trans8: '#eac99314',
    },
  },
  success: {
    default: {
      contrastText: '#ffffffff',
      color: '#ffffffff',
      dark: '#118d57ff',
      darker: '#065e49ff',
      light: '#77ed8bff',
      lighter: '#e1ffe0ff',
      main: '#22c55eff',
    },
    transparent: {
      trans12: '#22c55e1f',
      trans16: '#22c55e29',
      trans24: '#22c55e3d',
      trans32: '#22c55e52',
      trans48: '#22c55e7a',
      trans8: '#22c55e14',
    },
  },
  text: {
    primary: '#212B36',
    secondary: '#637381',
    disabled: '#919EAB',
  },
  warning: {
    default: {
      contrastText: '#212b36ff',
      dark: '#b76e00ff',
      darker: '#7a4100ff',
      light: '#ffd666ff',
      lighter: '#fff5ccff',
      main: '#ffab00ff',
    },
    transparent: {
      trans12: '#ffab001f',
      trans16: '#ffab0029',
      trans24: '#ffab003d',
      trans32: '#ffab0052',
      trans48: '#ffab007a',
      trans8: '#ffab0014',
    },
  },
};

export { acLightColors };
