import React from 'react';
import {StyleSheet, View} from 'react-native';
import {FOOTER_HEIGHT} from '../../styles';
import {Button} from 'react-native-paper';

type props = {
  titleNextButton?: string;
  titlePreviousButton?: string;
  onHandlePrevious?: () => void;
  onHandleNext?: () => void;
};

export const AcButtonActionFooter = React.memo(
  ({
    titleNextButton,
    titlePreviousButton,

    onHandlePrevious,
    onHandleNext,
  }: props) => {
    return (
      <View style={styles.footer}>
        <View style={styles.buttonActions}>
          {onHandlePrevious && (
            <Button
              mode="elevated"
              style={styles.button}
              onPress={() => {
                onHandlePrevious();
              }}>
              {titlePreviousButton || 'Quay lại'}
            </Button>
          )}

          {onHandleNext && (
            <Button
              mode="contained"
              style={styles.button}
              onPress={() => {
                onHandleNext();
              }}>
              {titleNextButton || 'Tiếp theo'}
            </Button>
          )}
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: FOOTER_HEIGHT, // Đảm bảo chiều cao footer
    backgroundColor: '#ffffff',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonActions: {
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    borderRadius: 8,
    flexBasis: 0,
    flexGrow: 1,
  },
});
