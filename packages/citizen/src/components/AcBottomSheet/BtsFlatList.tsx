import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import {CustomBottomSheet} from './CustomBottomSheet';
import {StyleSheet, View} from 'react-native';

type props = {
  children: React.ReactNode;
  refProp: React.RefObject<BottomSheetMethods>;
  snapPoints?: string[];

  onHandleChange: (index: number) => void;
};

export const BtsFlatList = React.memo(
  ({children, refProp, snapPoints = ['50%'], onHandleChange}: props) => {
    return (
      <CustomBottomSheet
        snapPoints={snapPoints}
        refProp={refProp}
        onHandleChange={onHandleChange}>
        <View style={styles.container}>{children}</View>
      </CustomBottomSheet>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
});
