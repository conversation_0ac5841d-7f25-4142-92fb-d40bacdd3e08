import BottomSheet from '@gorhom/bottom-sheet';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';

type props = {
  children: React.ReactNode;
  refProp: React.RefObject<BottomSheetMethods>;
  snapPoints?: string[];

  onHandleChange: (index: number) => void;
};

export const CustomBottomSheet = ({
  children,
  refProp,
  snapPoints = ['50%'],
  onHandleChange,
}: props) => {
  return (
    <BottomSheet
      enableDynamicSizing={false}
      enableContentPanningGesture={true}
      enablePanDownToClose
      onChange={(index: number) => onHandleChange(index)}
      snapPoints={snapPoints}
      ref={refProp}
      index={-1}>
      {children}
    </BottomSheet>
  );
};
