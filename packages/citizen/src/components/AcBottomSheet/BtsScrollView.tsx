import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {CustomBottomSheet} from './CustomBottomSheet';

type props = {
  title?: React.ReactNode;
  footer?: React.ReactNode;
  children: React.ReactNode;
  refProp: React.RefObject<BottomSheetMethods>;
  snapPoints?: string[];

  onHandleChange: (index: number) => void;
};

export const BtsScrollView = React.memo(
  ({
    title,
    footer,
    children,
    refProp,
    snapPoints = ['50%'],

    onHandleChange,
  }: props) => {
    return (
      <CustomBottomSheet
        snapPoints={snapPoints}
        refProp={refProp}
        onHandleChange={onHandleChange}>
        {title}
        <BottomSheetScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={[styles.contentContainer]}>
          {children}
        </BottomSheetScrollView>
        {footer && (
          <View style={styles.footerFixed}>
            {/* <Button
              mode="contained"
              style={styles.button}
              onPress={() => handleSubmitFormInfo()}>
              Xác nhận
            </Button> */}
            {footer}
          </View>
        )}
      </CustomBottomSheet>
    );
  },
);

const styles = StyleSheet.create({
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    zIndex: 1000,
    paddingBottom: 16,
  },
  footerFixed: {
    padding: 16,
    borderTopWidth: 1,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
});
