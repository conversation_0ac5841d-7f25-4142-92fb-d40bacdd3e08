import React from 'react';
import {Text, type TextProps} from 'react-native-paper';
import {AcTheme, useAcTheme} from '../AcThemeContext';

type AcTextProps = {
  color?: // | 'default'
  | 'primary'
    | 'secondary'
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'primaryText'
    | 'secondaryText'
    | 'disabledText';
  variant:
    | 'displayLarge'
    | 'displayMedium'
    | 'displaySmall'
    | 'headlineLarge'
    | 'headlineMedium'
    | 'headlineSmall'
    | 'titleLarge'
    | 'titleMedium'
    | 'titleSmall'
    | 'labelLarge'
    | 'labelMedium'
    | 'labelSmall'
    | 'bodyLarge'
    | 'bodyMedium'
    | 'bodySmall'
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'subtitle1'
    | 'subtitle2'
    | 'body1'
    | 'body2'
    | 'caption';
} & Omit<TextProps<string>, 'variant'>;

const getVariant = (variant: AcTextProps['variant']) => {
  switch (variant) {
    case 'h1':
      return 'displayLarge';
    case 'h2':
      return 'displayMedium';
    case 'h3':
      return 'displaySmall';
    case 'h4':
      return 'headlineLarge';
    case 'h5':
      return 'headlineMedium';
    case 'h6':
      return 'headlineSmall';
    case 'subtitle1':
      return 'titleLarge';
    case 'subtitle2':
      return 'titleMedium';
    case 'body1':
      return 'bodyLarge';
    case 'body2':
      return 'bodyMedium';
    case 'caption':
      return 'bodySmall';

    default:
      return variant;
  }
};

const AcText: React.FC<AcTextProps> = ({
  variant: iVariant,
  color = undefined,
  ...props
}) => {
  let variant = getVariant(iVariant);

  const {theme} = useAcTheme();

  const mColor: Partial<AcTheme['colors']> = {};

  if (color === 'primaryText') {
    mColor.onSurface = theme.ac.text.primary;
  } else if (color === 'secondaryText') {
    mColor.onSurface = theme.ac.text.secondary;
  } else if (color === 'disabledText') {
    mColor.onSurface = theme.ac.text.disabled;
  } else if (color) {
    mColor.onSurface = theme.ac[color].default.main;
  }

  return <Text {...props} variant={variant} theme={{colors: mColor}} />;
};

export {AcText};
