import * as React from 'react';
import {Text, View, Pressable, StyleProp, ViewStyle} from 'react-native';
import {useTheme} from 'react-native-paper';
import {viewStyles} from '../../styles';

export interface TabBarHomeData {
  label: string;
  activeIcon: React.JSX.Element;
  inActiveIcon: React.JSX.Element;
  styleItemActive?: string;
}
interface TabBarHomeProps {
  indexSelected: number;
  data: Array<TabBarHomeData>;
  containerStyle?: StyleProp<ViewStyle>;
  onPress: (index: number) => void;
}

export const TabBarHome = ({
  data,
  indexSelected,
  containerStyle,
  onPress,
}: TabBarHomeProps) => {
  const theme = useTheme();
  return (
    <View
      className={` ${viewStyles.viewRow} mx-4 rounded-lg bg-white`}
      style={containerStyle}>
      {data?.map((item, index) => {
        const isSelected = index === indexSelected;
        return (
          <Pressable
            key={index + ' '}
            onPress={() => {
              onPress && onPress?.(index);
            }}
            className={`${viewStyles.viewRow} ${
              viewStyles.center
            } flex-1 py-[19px] relative ${
              (isSelected && item?.styleItemActive) || ''
            }`}
            style={[
              isSelected && {
                borderBottomWidth: 2,
                borderBottomColor: theme.colors.primary,
              },
            ]}>
            {isSelected ? item?.activeIcon : item?.inActiveIcon}
            <Text
              style={[
                {
                  color: isSelected
                    ? theme.colors.primary
                    : theme.colors.outline,
                  fontWeight: 600,
                  marginHorizontal: 5,
                  opacity: isSelected ? 1 : 0.8,
                },
              ]}>
              {item?.label}
            </Text>
          </Pressable>
        );
      })}
    </View>
  );
};
