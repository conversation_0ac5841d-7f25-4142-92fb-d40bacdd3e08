import * as React from 'react';
import {Dialog, Portal} from 'react-native-paper';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {useAcTheme} from '../AcThemeContext';
import {AcText} from '../AcText';
import {AcButton} from '../AcButton';

type variantType =
  | 'primary'
  | 'secondary'
  | 'info'
  | 'success'
  | 'warning'
  | 'error'
  | 'inCall'
  | 'custom';
export type AcDialogProps = {
  children?: any;
  icon?: any;
  visible: any;
  actions?: any;
  loading?: boolean;
  okText?: string;
  cancelText?: string;
  title: string;
  onOk: (() => Promise<boolean>) | (() => void);
  onCancel: () => Promise<boolean> | void;
  maskClosable?: boolean;
  backgroundColor?: string;
  variant?: variantType;
  customIconComponent?: React.ReactNode;
  hideButton?: Array<'ok' | 'cancel'>;
  inCallButtonOK?: React.ReactNode;
  inCallButtonCancel?: React.ReactNode;
};

const IconSwitcher = ({
  variant,
  icon: Icon,
}: {
  variant: variantType;
  icon: any;
}) => {
  const {theme} = useAcTheme();
  let backgroundColor = theme.ac.primary.default.lighter;
  let iconColor = theme.ac.primary.default.main;
  if (variant === 'primary') {
    backgroundColor = theme.ac.primary.default.lighter;
    iconColor = theme.ac.primary.default.main;
  } else if (variant === 'secondary') {
    backgroundColor = theme.ac.secondary.default.lighter;
    iconColor = theme.ac.secondary.default.main;
  } else if (variant === 'info') {
    backgroundColor = theme.ac.info.default.lighter;
    iconColor = theme.ac.info.default.main;
  } else if (variant === 'success') {
    backgroundColor = theme.ac.success.default.lighter;
    iconColor = theme.ac.success.default.main;
  } else if (variant === 'warning') {
    backgroundColor = theme.ac.warning.default.lighter;
    iconColor = theme.ac.warning.default.main;
  } else if (variant === 'error') {
    backgroundColor = theme.ac.error.default.lighter;
    iconColor = theme.ac.error.default.main;
  } else {
    backgroundColor = theme.ac.primary.default.lighter;
    iconColor = theme.ac.primary.default.main;
  }
  return (
    <View style={{...styles.iconHolder, backgroundColor}}>
      <Icon size={32} color={iconColor} />
    </View>
  );
};

const AcDialog = ({
  children,
  title,
  icon: Icon,
  visible,
  actions = undefined,
  onOk,
  onCancel,
  okText = 'Ok',
  cancelText = 'Cancel',
  maskClosable = false,
  loading = undefined,
  backgroundColor,
  hideButton,
  variant = 'custom',
  inCallButtonCancel,
  inCallButtonOK,
}: AcDialogProps) => {
  const cCancel = async () => {
    if (onCancel) {
      await onCancel();
    }
  };

  return (
    <Portal>
      <Dialog
        theme={{colors: {elevation: {level3: backgroundColor}}}}
        visible={visible}
        dismissable={maskClosable}
        onDismiss={async () => {
          if (onCancel) {
            await onCancel();
          }
        }}>
        <Dialog.Icon
          // eslint-disable-next-line react/no-unstable-nested-components
          icon={() => {
            if (variant === 'inCall') {
              return null;
            }
            if (variant === 'custom') {
              return <>{Icon}</>;
            }

            if (Icon) {
              return <IconSwitcher variant={variant} icon={Icon} />;
            }
            return null;
          }}
        />
        <Dialog.Title style={styles.title}>{title}</Dialog.Title>
        <Dialog.Content>
          {typeof children === 'string' ? (
            <View style={styles.content}>
              <AcText variant="bodyMedium">{children}</AcText>
            </View>
          ) : (
            children
          )}
        </Dialog.Content>
        <Dialog.Actions>
          {!actions && (
            <View
              style={[
                styles.actions,
                variant === 'inCall'
                  ? {
                      justifyContent: 'space-around',
                    }
                  : {},
              ]}>
              {!hideButton?.includes('cancel') && variant !== 'inCall' && (
                <AcButton
                  style={styles.actionButton}
                  mode="soft"
                  onPress={async () => {
                    await cCancel();
                  }}>
                  {cancelText}
                </AcButton>
              )}
              {!hideButton?.includes('cancel') && variant === 'inCall' && (
                <TouchableOpacity
                  onPress={async () => {
                    await cCancel();
                  }}>
                  {inCallButtonCancel}
                </TouchableOpacity>
              )}
              {!hideButton?.includes('ok') && variant !== 'inCall' && (
                <AcButton
                  style={styles.actionButton}
                  mode="contained"
                  loading={typeof loading === 'boolean' ? loading : false}
                  color="primary"
                  onPress={async () => {
                    if (onOk) {
                      const result = await onOk();
                      if (typeof result === 'boolean') {
                        if (result === true) {
                          await cCancel();
                        }
                      } else {
                        await cCancel();
                      }
                    }
                  }}>
                  {okText}
                </AcButton>
              )}
              {!hideButton?.includes('ok') && variant === 'inCall' && (
                <TouchableOpacity
                  onPress={async () => {
                    if (onOk) {
                      const result = await onOk();
                      if (typeof result === 'boolean') {
                        if (result === true) {
                          await cCancel();
                        }
                      } else {
                        await cCancel();
                      }
                    }
                  }}>
                  {inCallButtonOK}
                </TouchableOpacity>
              )}
            </View>
          )}
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({
  title: {
    textAlign: 'center',
  },
  iconHolder: {
    borderRadius: 100,
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  actions: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  actionButton: {flex: 1},
});

export {AcDialog};
