import React from 'react';
import {Button, type ButtonProps} from 'react-native-paper';
import {AcTheme, useAcTheme} from '../AcThemeContext';

type AcButtonProps = {
  color?:
    | 'primary'
    | 'secondary'
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'default';
  mode?: ButtonProps['mode'] | 'soft';
} & Omit<ButtonProps, 'mode'>;

const AcButton: React.FC<AcButtonProps> = ({
  color = 'default',
  mode = 'text',
  disabled,
  ...props
}) => {
  const {theme} = useAcTheme();

  const mColor: Partial<AcTheme['colors']> = {};
  if (!disabled) {
    if (color === 'default') {
      switch (mode) {
        case 'contained':
          mColor.primary = theme.ac.component.button.inherit.background;
          mColor.onPrimary = theme.ac.component.button.inherit.text;
          break;
        case 'outlined':
          mColor.outline = theme.ac.component.button.outlined;
          mColor.primary = theme.ac.text.primary;
          break;
        case 'text':
          // mColor.primary = theme.ac[color].default.main;
          break;
        case 'elevated':
          // mColor.primary = theme.ac[color].default.main;
          break;
        case 'soft':
          mColor.onPrimary = theme.ac.text.primary;
          mColor.primary = theme.ac.grey.transparent.greyTrans8;
          break;
        default:
          break;
      }
    } else {
      switch (mode) {
        case 'contained':
          mColor.primary = theme.ac[color].default.main;
          mColor.onPrimary = theme.ac[color].default.contrastText;
          break;
        case 'outlined':
          mColor.outline = theme.ac[color].default.main;
          mColor.primary = theme.ac[color].default.main;
          break;
        case 'text':
          mColor.primary = theme.ac[color].default.main;
          break;
        case 'elevated':
          mColor.primary = theme.ac[color].default.main;
          break;
        case 'soft':
          mColor.primary = theme.ac[color].transparent.trans8;
          mColor.onPrimary = theme.ac[color].default.main;
          break;
        default:
          break;
      }
    }
  }

  // switch (color) {
  //   case 'primary':
  //     mColor.primary = theme.ac.primary.default.main;

  //     switch (mode) {
  //       case 'contained':
  //         mColor.onPrimary = theme.ac[color].default.contrastText;
  //         break;
  //       case 'outlined':
  //         mColor.outline = theme.ac[color].default.main;
  //         break;

  //       default:
  //         break;
  //     }
  //     break;
  //   case 'info':
  //     mColor.primary = theme.ac.info.default.main;
  //     mColor.onPrimary = theme.ac.info.default.contrastText;
  //     break;

  //   default:
  //     break;
  // }

  // mColor.border = 'red';
  // mColor.inversePrimary = 'red';
  // mColor.primary = theme.ac.info.default.main;
  // mColor.outline = 'red';
  // mColor.onPrimary = 'white';

  return (
    <Button
      {...props}
      mode={mode === 'soft' ? 'contained' : mode}
      disabled={disabled}
      theme={{colors: mColor}}
      // style={{ backgroundColor: 'red' }}
    />
  );
};

export {AcButton};
