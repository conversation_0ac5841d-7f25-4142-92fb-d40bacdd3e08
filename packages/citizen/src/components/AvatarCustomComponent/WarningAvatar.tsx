import React from 'react';
import {View, StyleSheet} from 'react-native';
import Svg, {Circle, Path} from 'react-native-svg';

interface WarningAvatarProps {
  size?: number; // <PERSON><PERSON>ch thước của avatar
  backgroundColor?: string; // Màu nền
  iconColor?: string; // Màu tam giác và dấu chấm than
}

const WarningAvatar: React.FC<WarningAvatarProps> = ({
  size = 60,
  backgroundColor = '#FFF8E1',
  iconColor = '#FFC107',
}) => {
  const iconSize = size * 0.5; // Tam giác chiếm 50% của avatar

  return (
    <View style={[styles.container, {width: size, height: size}]}>
      <Svg width={size} height={size} viewBox="0 0 60 60">
        {/* Nền */}
        <Circle cx="30" cy="30" r="30" fill={backgroundColor} />
        {/* <PERSON> gi<PERSON> c<PERSON> báo */}
        <Path
          d={`M30 ${30 - iconSize / 2} L${30 + iconSize / 2} ${
            30 + iconSize / 2
          } H${30 - iconSize / 2} Z`}
          fill={iconColor}
        />
        {/* D<PERSON>u chấm than */}
        <Path
          d={`M${30 - iconSize * 0.05} ${30 - iconSize * 0.15} H${
            30 + iconSize * 0.05
          } V${30 + iconSize * 0.2} H${30 - iconSize * 0.05} Z`}
          fill="#FFFFFF"
        />
        <Circle
          cx="30"
          cy={30 + iconSize * 0.3}
          r={iconSize * 0.05}
          fill="#FFFFFF"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 9999, // Đảm bảo hình tròn
    overflow: 'hidden',
  },
});

export default WarningAvatar;
