import React from 'react';
import {Dimensions, StyleSheet, TouchableOpacity, View} from 'react-native';
import {Text, useTheme} from 'react-native-paper';
import Icon from 'react-native-vector-icons/Ionicons';

export type IOptionMenuItem = {
  id: number;
  name: string;
};

type props = {
  currentStep: number;
  options: IOptionMenuItem[];
  onStepPress?: (id: number) => void;
};

export const StepMenuVertical = React.memo(
  ({currentStep, options, onStepPress}: props) => {
    // COMMENT: State
    const [label, setLabel] = React.useState<string>('');
    const theme = useTheme();
    const screenWidth = Dimensions.get('window').width;

    React.useEffect(() => {
      if (options.length > 0) {
        const stepExist = options.find(step => step.id === currentStep);
        if (stepExist) {
          setLabel(stepExist.name);
        }
      }
    }, [currentStep, options]);

    const connectorWidth = screenWidth / (options.length * 1.5);

    return (
      <View style={[styles.wrapper, {backgroundColor: theme.colors.surface}]}>
        <View style={styles.container}>
          {options.map((step: IOptionMenuItem, index) => (
            <View key={step.id}>
              <View style={styles.stepWrapper}>
                {/* Step Circle */}
                <TouchableOpacity
                  onPress={() => onStepPress && onStepPress(step.id)}
                  style={[
                    styles.circle,
                    {
                      backgroundColor:
                        currentStep === step.id
                          ? theme.colors.primary
                          : theme.colors.surfaceVariant,
                      borderWidth: 2,
                      borderColor:
                        currentStep === step.id
                          ? theme.colors.primary
                          : theme.colors.outline,
                    },
                  ]}>
                  {step.id < currentStep ? (
                    <Icon
                      name="checkmark-circle"
                      size={24}
                      color={theme.colors.secondary}
                    />
                  ) : (
                    <Text
                      style={[
                        styles.stepNumber,
                        {
                          color:
                            currentStep === step.id
                              ? theme.colors.onPrimary
                              : theme.colors.onSurface,
                        },
                      ]}>
                      {step.id}
                    </Text>
                  )}
                </TouchableOpacity>

                {/* Connector */}
                {index < options.length - 1 && (
                  <View
                    style={[
                      styles.connector,
                      {width: connectorWidth},
                      {
                        backgroundColor:
                          index < currentStep - 1
                            ? theme.colors.secondary
                            : theme.colors.outline,
                      },
                    ]}
                  />
                )}
              </View>
            </View>
          ))}
        </View>
        <View>
          <Text variant="labelLarge" style={{color: theme.colors.onSurface}}>
            Bước {currentStep}: {label}
          </Text>
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: 12,
    padding: 16,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  // activeCircle and inactiveCircle are now handled inline with theme
  stepNumber: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  // activeStepNumber and inactiveStepNumber are now handled inline with theme
  stepLabel: {
    marginLeft: 8,
    fontSize: 14,
  },
  // activeStepLabel and inactiveStepLabel are now handled inline with theme
  connector: {
    height: 2,
  },
  // currentStepNumber is now handled inline with theme

  // completedConnector is now handled inline with theme
});
