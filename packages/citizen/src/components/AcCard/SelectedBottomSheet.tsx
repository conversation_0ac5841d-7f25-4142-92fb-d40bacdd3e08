import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {Button, Card} from 'react-native-paper';
import {textStyles} from '../../styles/QNH_textStyle';

type props = {
  name?: string;
  fileName?: string;
  code: string;
  onOpenBottomSheet: (code: string) => void;
  onRemoveFile: (code: string) => void;
};

export const SelectedBottomSheet = ({
  name,
  fileName,
  code,
  onOpenBottomSheet,
  onRemoveFile,
}: props) => {
  return (
    <Card style={styles.card}>
      <Card.Content>
        <View>
          <Text className={`${textStyles.subTitle2}`}>{name}</Text>
        </View>
      </Card.Content>
      <View>
        {fileName ? (
          <View></View>
        ) : (
          <View style={styles.buttonActions}>
            <Button
              style={styles.button}
              mode="contained"
              onPress={() => onOpenBottomSheet(code)}>
              <PERSON><PERSON><PERSON> g<PERSON> tờ
            </Button>
          </View>
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    backgroundColor: '#fff',
  },
  buttonActions: {
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    borderRadius: 8,
    flexBasis: 0,
    flexGrow: 1,
  },
});
