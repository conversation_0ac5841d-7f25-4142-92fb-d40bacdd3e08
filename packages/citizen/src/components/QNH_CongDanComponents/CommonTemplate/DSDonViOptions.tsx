import React from 'react';
import {IDsDonViOption, useCommonProcedure} from '../../../stores';
import {EIsLoading} from '../../../types';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {textStyles} from '../../../styles/QNH_textStyle';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import FeatherIcon from 'react-native-vector-icons/Feather';
import IoniconsIcon from 'react-native-vector-icons/Ionicons';
import {BottomSheetMethods} from '@gorhom/bottom-sheet/lib/typescript/types';
import {BtsFlatList} from '../../AcBottomSheet';
import {filterSelectOptions} from '../../../utils';
import {Button} from 'react-native-paper';

type props = {
  refProp: React.RefObject<BottomSheetMethods>;
  selectedData: string | null;

  onHandleChange?: (index: number) => void;
  onHandleSelect: (value: IDsDonViOption) => void;
};

export const DSDonViOptions = React.memo(
  ({refProp, selectedData, onHandleChange, onHandleSelect}: props) => {
    // COMMENT: Store
    const {dsDonViThucHienOptions, isLoadingApi} = useCommonProcedure();

    // COMMENT: State
    const [data, setData] = React.useState<IDsDonViOption[]>([]);
    const [displayData, setDisplayData] = React.useState<IDsDonViOption[]>([]);
    const [page, setPage] = React.useState(1);
    const [loading, setLoading] = React.useState(false);
    const [selectedId, setSelectedId] = React.useState<string | null>(
      selectedData,
    );
    const [textSearch, setTextSearch] = React.useState('');

    // COMMENT: Function
    const loadMore = () => {
      if (loading || displayData.length >= data.length) {
        return;
      }
      setLoading(true);
      const nextPageData = data.slice(page * 20, (page + 1) * 20);
      setTimeout(() => {
        setDisplayData(prev => [...prev, ...nextPageData]);
        setPage(prevPage => prevPage + 1);
        setLoading(false);
      }, 1000); // Simulate network delay
    };

    const searchFilter = React.useCallback(
      (text: string) => {
        try {
          let filteredData;
          if (text !== '') {
            filteredData = dsDonViThucHienOptions.filter(
              (item: IDsDonViOption) =>
                filterSelectOptions(text, {label: item.tenDonVi}),
            );
          } else {
            filteredData = [...dsDonViThucHienOptions];
          }
          setData(filteredData);
          setDisplayData(filteredData.slice(0, 20));
          setPage(1);
        } catch (error) {
          console.error('Error in searchFilter:', error);
        } finally {
          setLoading(false); // Ensure loading is reset
        }
      },
      [dsDonViThucHienOptions], // Only depends on the data array
    );

    const handleSelect = React.useCallback((id: string) => {
      setSelectedId(id);
    }, []);

    const handleSelectDonVi = React.useCallback(() => {
      if (!selectedId) {
        return;
      }
      const selectedDonVi = dsDonViThucHienOptions.find(
        (item: IDsDonViOption) => `${item.donViID}` === selectedId,
      );
      onHandleSelect(selectedDonVi as IDsDonViOption);
    }, [selectedId, dsDonViThucHienOptions, onHandleSelect]);

    // COMMENT: useEffect
    React.useEffect(() => {
      if (isLoadingApi === EIsLoading.SUCCESS) {
        setData(dsDonViThucHienOptions);
        setDisplayData(dsDonViThucHienOptions.slice(0, 20));
      }
    }, [isLoadingApi, dsDonViThucHienOptions]);

    React.useEffect(() => {
      const handler = setTimeout(() => {
        searchFilter(textSearch);
      }, 400);

      return () => {
        clearTimeout(handler); // Clear debounce timeout
      };
    }, [textSearch, searchFilter]); // Only depends on textSearch

    return (
      <BtsFlatList
        refProp={refProp}
        snapPoints={['70%']}
        onHandleChange={(index: number) => {
          onHandleChange && onHandleChange(index);
        }}>
        <View style={styles.searchContainer}>
          <FeatherIcon
            name="search"
            size={24}
            color="#B0B0B0"
            style={styles.icon}
          />
          <TextInput
            className={`${textStyles.body1}`}
            placeholder="Tìm kiếm giấy tờ"
            placeholderTextColor="#B0B0B0"
            value={textSearch}
            onChangeText={setTextSearch}
          />
        </View>
        <BottomSheetFlatList
          style={styles.list}
          data={displayData}
          keyExtractor={(item: IDsDonViOption, index: number) =>
            `${item.donViID}_${index}`
          }
          contentContainerStyle={styles.contentContainerStyle}
          renderItem={({item}) => (
            <TouchableOpacity
              key={`${item.donViID}`}
              style={[
                styles.item,
                selectedId === `${item.donViID}` && styles.selectedItem,
              ]}
              onPress={() => handleSelect(`${item.donViID}`)}>
              <View style={styles.itemContent}>
                {selectedId === `${item.donViID}` && (
                  <IoniconsIcon
                    name="checkmark-circle"
                    size={24}
                    color="red"
                    style={styles.icon}
                  />
                )}
                <View>
                  <Text className={`${textStyles.subTitle2}`}>
                    {item.tenDonVi}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
          onEndReached={loadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={
            loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size={32} />
              </View>
            ) : null
          }
        />
        <View>
          <Button
            mode="contained"
            style={styles.button}
            onPress={() => handleSelectDonVi()}>
            Xác nhận
          </Button>
        </View>
      </BtsFlatList>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  list: {
    paddingVertical: 16,
  },
  contentContainerStyle: {paddingBottom: 16},
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#B0B0B0',
    borderRadius: 8,
    paddingHorizontal: 14,
    backgroundColor: '#FFFFFF',
  },
  icon: {
    marginRight: 8,
  },
  item: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  selectedItem: {
    backgroundColor: '#f0f0f0',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 24,
  },
  button: {
    borderRadius: 8,
  },
  contentBottomSheet: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
});
