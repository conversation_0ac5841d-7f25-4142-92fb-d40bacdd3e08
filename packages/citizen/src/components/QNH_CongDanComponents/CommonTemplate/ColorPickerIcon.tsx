import React from 'react';
import {CustomIcon} from '../../../icons';

interface ColorPickerIconProps {
  paint?: string[]; // Xác định kiểu hiển thị
}

export const ColorPickerIcon = React.memo(
  ({paint = ['#8DB6FC', '#366AE2']}: ColorPickerIconProps) => {
    const [colors, setColors] = React.useState({
      paint0: ['#8DB6FC', '#366AE2'],
      paint1: ['#8DB6FC', '#366AE2'],
      paint2: ['#8DB6FC', '#366AE2'],
      paint3: ['#8DB6FC', '#366AE2'],
    });

    React.useEffect(() => {
      const newColors = {
        paint0: paint,
        paint1: paint,
        paint2: paint,
        paint3: paint,
      };
      setColors(newColors);
    }, [paint]);

    return <CustomIcon gradientColors={colors} />;
  },
);
