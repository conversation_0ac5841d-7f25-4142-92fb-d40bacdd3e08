import React, {useState} from 'react';
import {CCAppBar} from '../HeaderComponent/CCAppBar';
import {useCommonProcedure} from '../../../stores';

type props = {
  title?: string;
};

export const HeaderCommonProcedure = React.memo(
  ({title = 'Thủ tục phổ biến'}: props) => {
    // return <CCAppBar isBack label={title} />;

    // COMMENT: Store
    const {updateTextSearchProcedureItem} = useCommonProcedure();

    // COMMENT: state
    const [textSearch, setTextSearch] = useState<string>('');

    // COMMENT: function
    const handleChangeTextSearch = (text: string) => {
      if (text === '' || !text) {
        updateTextSearchProcedureItem(undefined);
      } else {
        updateTextSearchProcedureItem(text);
      }
      setTextSearch(text);
    };

    return (
      <CCAppBar
        isBack
        showSearch={true}
        onSearchChange={(query: string) => handleChangeTextSearch(query)}
        searchValue={textSearch}
      />
    );
  },
);
