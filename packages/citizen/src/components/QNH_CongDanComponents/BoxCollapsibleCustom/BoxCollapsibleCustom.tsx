import React from 'react';
import {
  LayoutAnimation,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useTheme} from 'react-native-paper';
import {textStyles} from '../../../styles/QNH_textStyle';
import Icon from 'react-native-vector-icons/MaterialIcons';

type props = {
  startIcon?: React.ReactNode;
  subTitle?: React.ReactNode;
  title?: string;
  content: React.ReactNode;
};

export const BoxCollapsibleCustom = React.memo(
  ({startIcon, subTitle, title, content}: props) => {
    const [expanded, setExpanded] = React.useState(true);
    const theme = useTheme();

    const toggleExpand = () => {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setExpanded(!expanded);
    };

    return (
      <View style={styles.container}>
        {/* Header */}
        <TouchableOpacity
          activeOpacity={0.7}
          className="flex-row justify-between items-center px-1 py-2"
          onPress={toggleExpand}>
          <View className="flex-row items-center">
            {startIcon ? <View className="py-1 pr-1">{startIcon}</View> : null}

            <View className="flex-col gap[4px]">
              <Text className={`${textStyles.h6}`}>{title}</Text>
              {subTitle ? (
                <View className="mt-[4px]">
                  <Text className={`${textStyles.body2}`}>
                    {subTitle || '_'}
                  </Text>
                </View>
              ) : null}
            </View>
          </View>
          <Icon
            name={expanded ? 'keyboard-arrow-up' : 'keyboard-arrow-down'}
            size={24}
            color={theme.colors.outline}
          />
        </TouchableOpacity>

        {/* Content */}
        {expanded ? content : null}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    // padding: 16,
  },
});
