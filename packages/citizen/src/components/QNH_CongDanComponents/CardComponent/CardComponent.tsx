import React, {memo} from 'react';
import {Text, View, StyleProp, ViewStyle, TextStyle} from 'react-native';
import {Button, Card, useTheme} from 'react-native-paper';
import {StyleSheet} from 'react-native';

export type CardComponentProps = {
  title: React.ReactNode;
  onPress?: () => void;
  children: React.ReactNode;
  button?: React.ReactNode;
  cardActions?: React.ReactNode;
  buttonMode?:
    | 'text'
    | 'outlined'
    | 'contained'
    | 'elevated'
    | 'contained-tonal';
  buttonStyle?: StyleProp<ViewStyle>;
  buttonLabelStyle?: StyleProp<TextStyle>;
  buttonTextStyle?: StyleProp<TextStyle>;
};

export const CardComponent = memo(
  ({
    title,
    onPress,
    children,
    button = 'Xem tất cả',
    cardActions,
    buttonMode = 'text',
    buttonStyle,
    buttonLabelStyle,
  }: CardComponentProps) => {
    const theme = useTheme();
    return (
      <Card style={{backgroundColor: theme.colors.background}}>
        <Card.Content style={[styles.cardContent]}>
          <View style={styles.cardTitle}>
            <Text
              style={[styles.cardTitleText, {color: theme.colors.onSurface}]}>
              {title}
            </Text>
            {onPress && (
              <Button
                mode={buttonMode}
                onPress={onPress}
                style={[styles.defaultButton, buttonStyle]}
                labelStyle={[styles.defaultButtonText, buttonLabelStyle]}>
                {button}
              </Button>
            )}
          </View>
          <View>{children}</View>
        </Card.Content>
        <Card.Actions style={(cardActions && styles.cardActions) || {}}>
          {cardActions}
        </Card.Actions>
      </Card>
    );
  },
);

const styles = StyleSheet.create({
  cardTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between', // Distributes content evenly
    alignItems: 'center',
    padding: 0,
  },
  cardTitleText: {
    fontSize: 20,
    fontWeight: '600',
  },
  cardContent: {
    padding: 0,
  },
  cardActions: {
    padding: 0,
  },
  defaultButton: {
    marginRight: -8, // Adjust to align with card edge
  },
  defaultButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
});
