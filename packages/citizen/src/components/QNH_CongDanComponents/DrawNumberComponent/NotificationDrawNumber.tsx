import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import WarningAvatar from '../../AvatarCustomComponent/WarningAvatar';
import {Button, useTheme} from 'react-native-paper';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {useDrawNumberStore} from '../../../stores/drawNumber.store';

type props = {
  onHandleShowButtonSheetBranchList?: () => void; // Made optional since we're using navigation
};

export const NotificationDrawNumber = React.memo(
  ({onHandleShowButtonSheetBranchList: _}: props) => {
    const navigation = useNavigation<NavigationProp<MainStackParamList>>();
    const theme = useTheme();
    const {clearSelection} = useDrawNumberStore();
    const handleNavigateToBranchSelect = () => {
      clearSelection();
      navigation.navigate('DrawNumberBranchSelect');
    };

    return (
      <View style={[styles.container, {backgroundColor: theme.colors.surface}]}>
        <View style={styles.content}>
          <View style={styles.imageContent}>
            <WarningAvatar />
            <Text
              style={[
                {
                  fontSize: 20,
                  fontWeight: '600',
                  color: theme.colors.onSurface,
                },
              ]}>
              Vui lòng chỉ lấy số khi thật sự cần
            </Text>
          </View>

          <View>
            <Text
              style={[{fontSize: 16, color: theme.colors.onSurfaceVariant}]}>
              Việc bỏ số sẽ ảnh hưởng đến người khác và có thể làm quý khách bị
              hạn chế quyền lấy số.
            </Text>
            <Text
              style={[{fontSize: 16, color: theme.colors.onSurfaceVariant}]}>
              Cảm ơn quý khách đã hợp tác vì lợi ích chung!
            </Text>
          </View>
        </View>

        <Button
          mode="contained"
          style={[styles.button, {backgroundColor: theme.colors.primary}]}
          onPress={handleNavigateToBranchSelect}
          labelStyle={{
            color: theme.colors.onPrimary,
            fontSize: 18,
            fontWeight: '600',
          }}>
          Tôi đã hiểu
        </Button>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
    justifyContent: 'space-between',
    minHeight: 300, // Ensure minimum height for proper display
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
    flex: 1,
  },
  imageContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 16,
    paddingBottom: 16,
  },
  button: {
    borderRadius: 8,
    marginTop: 16,
  },
});
