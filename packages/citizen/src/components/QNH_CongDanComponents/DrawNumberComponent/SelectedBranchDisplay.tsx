import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useDrawNumberStore} from '../../../stores/drawNumber.store';
import {textStyles} from '../../../styles/QNH_textStyle';

export const SelectedBranchDisplay: React.FC = () => {
  const {selectedBranch} = useDrawNumberStore();

  if (!selectedBranch) {
    return (
      <View style={styles.container}>
        <Text className={`${textStyles.body2}`} style={styles.text}>
          Ch<PERSON>a chọn chi nhánh
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text className={`${textStyles.h6}`} style={styles.title}>
        Chi nhánh đã chọn:
      </Text>
      <Text className={`${textStyles.body1}`} style={styles.branchName}>
        {selectedBranch.name}
      </Text>
      {selectedBranch.address && (
        <Text className={`${textStyles.body2}`} style={styles.branchAddress}>
          {selectedBranch.address}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  branchName: {
    color: '#007AFF',
    fontWeight: '500',
    marginBottom: 4,
  },
  branchAddress: {
    color: '#666',
  },
  text: {
    color: '#666',
    textAlign: 'center',
  },
});
