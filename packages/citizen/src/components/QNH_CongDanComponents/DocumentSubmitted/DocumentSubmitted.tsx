import React from 'react';
import {View} from 'react-native';
import {CardComponent} from '../CardComponent/CardComponent';
import {LayoutComponent} from '../LayoutComponent/LayoutComponent';
import {useNavigation} from '@react-navigation/native';

import {useShareProfiles} from '../../../hooks/useShareProfiles';
import {ShareItem} from '../../../components/ShareItem';

export const DocumentSubmitted = React.memo(() => {
  const nav = useNavigation();

  // Use the same hook as SharesScreen
  const {data} = useShareProfiles({});

  // Flatten and map to IShareDocument[]
  const items = React.useMemo(() => {
    if (!data?.pages) return [];
    const flattened = data.pages.flatMap(page => {
      if (page?.data?.items) return page.data.items;
      if (page?.data && Array.isArray(page.data)) return page.data;
      if (Array.isArray(page)) return page;
      if (page && typeof page === 'object') return [page];
      return [];
    });
    return flattened.slice(0, 3);
  }, [data]);

  const handleOnPress = () => {
    nav.navigate('SharesScreen');
  };

  return (
    <View>
      <CardComponent
        title="Hồ sơ đã nộp gần đây"
        onPress={() => handleOnPress()}>
        <LayoutComponent type="vertical">
          {items.map((shareItem, index) => (
            <ShareItem key={shareItem.id || index} item={shareItem} />
          ))}
        </LayoutComponent>
      </CardComponent>
    </View>
  );
});
