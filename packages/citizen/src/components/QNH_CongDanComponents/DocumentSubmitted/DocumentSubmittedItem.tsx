import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import {Card, Badge} from 'react-native-paper';
import {textStyles} from '../../../styles/QNH_textStyle';

type DocumentListItemProps = {
  title: string;
  code: string;
  date: string;
  status: string;
  statusColor: string;
  id: string;
};

export const DocumentSubmittedItem: React.FC<DocumentListItemProps> = ({
  title,
  code,
  date,
  status,
  statusColor,
  id,
}) => {
  return (
    <Card style={styles.card} key={id}>
      <Card.Content style={styles.cardContent}>
        <View style={styles.detailsContainer}>
          <Text
            className={`${textStyles.subTitle2}`}
            style={styles.title}
            numberOfLines={1}
            ellipsizeMode="tail">
            {title || '---'}
          </Text>
          <Text className={`${textStyles.body2}`} style={styles.detail}>
            M<PERSON> hồ sơ: {code || '---'}
          </Text>
          <Text className={`${textStyles.body2}`} style={styles.detail}>
            Ngày nộp: {date || '---'}
          </Text>
        </View>
        <View>
          <Badge
            style={[
              styles.badge,
              {
                backgroundColor: statusColor || '#ccc',
                minWidth: status.length,
              },
            ]}
            className={`${textStyles.label}`}
            size={20}>
            {status || '---'}
          </Badge>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    backgroundColor: '#fff',
  },
  cardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 0,
  },
  detailsContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 4,
  },
  detail: {
    marginBottom: 2,
  },
  badge: {
    color: '#fff',
    textAlign: 'center',
    paddingHorizontal: 6,
    borderRadius: 6,
  },
});
