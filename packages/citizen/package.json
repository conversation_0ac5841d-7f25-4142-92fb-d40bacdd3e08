{"name": "citizen", "version": "0.0.1", "private": true, "scripts": {"align-deps": "rnx-align-deps --write", "bundle": "pnpm bundle:ios && pnpm bundle:android", "bundle:android": "react-native webpack-bundle --platform android --entry-file index.js --dev false", "bundle:ios": "react-native webpack-bundle --platform ios --entry-file index.js --dev false", "check-deps": "rnx-align-deps", "copy:env": "node scripts/copy-env.js", "switch:client": "node scripts/copy-client-files.js", "start": "node scripts/start.js", "start HN dev": "node scripts/start.js HN dev", "test": "jest --passWithNoTests"}, "dependencies": {"@ac-mobile/common": "0.3.0", "@gorhom/bottom-sheet": "^5.1.1", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-community/netinfo": "11.3.1", "@react-navigation/bottom-tabs": "7.1.3", "@react-navigation/material-top-tabs": "7.1.0", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.1.14", "@shopify/flash-list": "^1.8.3", "axios": "0.27.2", "base-64": "^1.0.0", "deepmerge": "^4.3.1", "jspdf": "^3.0.1", "jwt-decode": "3.1.2", "lodash": "^4.17.21", "lottie-react-native": "6.7.2", "moment": "2.30.1", "nativewind": "2.0.11", "react": "18.2.0", "react-hook-form": "^7.59.0", "react-native": "0.74.5", "react-native-app-auth": "^7.2.0", "react-native-device-info": "14.0.2", "react-native-document-picker": "9.3.1", "react-native-draggable-flatlist": "^4.0.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.21.2", "react-native-heic-converter": "^1.3.3", "react-native-html-to-pdf": "^0.12.0", "react-native-image-crop-picker": "0.41.6", "react-native-image-picker": "^8.2.1", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.0", "react-native-paper": "5.12.5", "react-native-paper-dates": "^0.22.34", "react-native-pdf": "6.7.7", "react-native-permissions": "5.2.1", "react-native-reanimated": "3.16.6", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.1.0", "react-native-screens": "4.3.0", "react-native-svg": "14.1.0", "react-native-tab-view": "4.0.5", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "10.2.0", "react-native-video": "^6.16.0", "react-native-webview": "13.12.5", "react-query": "^3.39.3", "tailwindcss": "3.3.2", "xml-parser": "^1.2.1", "yup": "^1.6.1", "zustand": "^5.0.2"}, "devDependencies": {"@ac-mobile/sdk": "0.0.19", "@babel/core": "7.25.9", "@babel/preset-env": "7.25.9", "@babel/runtime": "7.25.9", "@callstack/repack": "^4.3.3", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "0.74.87", "@rnx-kit/align-deps": "3.0.1", "@tsconfig/react-native": "^2.0.3", "@types/jest": "^29.2.1", "@types/lodash": "^4.17.19", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "@types/xml-parser": "^1.2.33", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "babel-jest": "^29.6.3", "babel-loader": "^9.2.1", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "terser-webpack-plugin": "^5.3.10", "typescript": "5.0.4", "webpack": "^5.95.0"}, "federatedModule": "citizen", "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/@ac-mobile/sdk/preset"], "requirements": ["@ac-mobile/sdk@0.0.15"], "capabilities": ["super-app"]}}}