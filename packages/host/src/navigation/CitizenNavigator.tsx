import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
// import TabsNavigator from './TabsNavigator';

import CitizenMiniAppScreen from '../screens/CitizenMiniAppScreen';

export type MainStackParamList = {
  Home: undefined;
};

const Main = createNativeStackNavigator<MainStackParamList>();

const CitizenNavigator = () => {
  return (
    <Main.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Main.Screen name="Home" component={CitizenMiniAppScreen} />
      {/* <Main.Screen name="Home" component={TabsNavigator} /> */}
    </Main.Navigator>
  );
};

export default CitizenNavigator;
