import {Federated} from '@callstack/repack/client';
import React from 'react';
import ErrorBoundary from '../components/ErrorBoundary';
import SplashScreen from '../components/SplashScreen';

const Citizen = React.lazy(() => Federated.importModule('citizen', './App'));

const CitizenMiniAppScreen = () => {
  return (
    <ErrorBoundary name="CitizenMiniAppScreen">
      <React.Suspense fallback={<SplashScreen />}>
        <Citizen />
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default CitizenMiniAppScreen;
