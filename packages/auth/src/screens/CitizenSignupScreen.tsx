/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {
  Alert,
  ImageBackground,
  Keyboard,
  Platform,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import {Button, Text, TextInput, HelperText} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {API_CONFIG} from '../api/api-config';
import {Assets} from '../assets';
import {CopyrightText} from '../components/CopyrightText';
import moment from 'moment';
// Import DatePicker with lazy loading to prevent native module errors
const DatePicker = React.lazy(() => import('react-native-date-picker'));

const CitizenSignupScreen = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Form fields
  const [identificationId, setIdentificationId] = useState('');
  const [fullName, setFullName] = useState('');
  const [gender, setGender] = useState('0'); // 0: Male, 1: Female
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState<Date | null>(null);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [secureTextEntry, setSecureTextEntry] = useState(true);
  const [secureConfirmTextEntry, setSecureConfirmTextEntry] = useState(true);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Mark field as touched when user interacts with it
  const handleFieldTouch = (field: string) => {
    setTouched(prev => ({...prev, [field]: true}));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate ID
    if (!identificationId) {
      newErrors.identificationId = 'Vui lòng nhập số CCCD/CMT';
    } else if (!/^\d{9}(\d{3})?$/.test(identificationId)) {
      newErrors.identificationId =
        'Số CCCD/CMT không đúng định dạng (9 hoặc 12 số)';
    }

    // Validate full name
    if (!fullName.trim()) {
      newErrors.fullName = 'Vui lòng nhập họ và tên';
    }

    // Validate email
    if (!email) {
      newErrors.email = 'Vui lòng nhập email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Email không đúng định dạng';
    }

    // Validate phone number
    if (!phoneNumber) {
      newErrors.phoneNumber = 'Vui lòng nhập số điện thoại';
    } else if (!/^(0|\+84)[3|5|7|8|9][0-9]{8}$/.test(phoneNumber)) {
      newErrors.phoneNumber = 'Số điện thoại không đúng định dạng';
    }

    // Validate date of birth
    if (!dateOfBirth) {
      newErrors.dateOfBirth = 'Vui lòng chọn ngày sinh';
    } else {
      // Check if user is at least 18 years old
      const today = new Date();
      const eighteenYearsAgo = new Date(
        today.getFullYear() - 18,
        today.getMonth(),
        today.getDate(),
      );

      if (dateOfBirth > eighteenYearsAgo) {
        newErrors.dateOfBirth = 'Bạn phải từ 18 tuổi trở lên';
      }
    }

    // Validate password
    if (!password) {
      newErrors.password = 'Vui lòng nhập mật khẩu';
    } else if (password.length < 6) {
      newErrors.password = 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    // Validate confirm password
    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Mật khẩu xác nhận không khớp';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async () => {
    // Mark all fields as touched on submission attempt
    setTouched({
      identificationId: true,
      fullName: true,
      email: true,
      phoneNumber: true,
      dateOfBirth: true,
      password: true,
      confirmPassword: true,
    });

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Example API call
      const response = await fetch(`${API_CONFIG.ENDPOINT}/User/Register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identificationId,
          fullName,
          gender: parseInt(gender, 10),
          email,
          phoneNumber,
          dateOfBirth: dateOfBirth?.toISOString(),
          password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        Alert.alert(
          'Thành công',
          'Đăng ký tài khoản thành công. Bạn có thể đăng nhập ngay bây giờ.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ],
        );
      } else {
        Alert.alert(
          'Lỗi',
          data.message || 'Có lỗi xảy ra khi đăng ký tài khoản',
        );
      }
    } catch (error) {
      console.error('Signup error:', error);
      Alert.alert(
        'Lỗi',
        'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        <ImageBackground
          source={Assets.bgGradient}
          style={StyleSheet.absoluteFill}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgLoginTop}
          style={styles.bgLoginTop}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgCity}
          style={styles.bgCity}
          resizeMode="contain"
        />
        <KeyboardAvoidingView
          style={styles.centered}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled">
            <View style={styles.formContainer}>
              <Text style={styles.title}>Đăng ký tài khoản</Text>
              <Text style={styles.subtitle}>
                Vui lòng điền đầy đủ thông tin bên dưới để đăng ký tài khoản
              </Text>

              {/* Identification ID */}
              <View style={[styles.inputGroup, {marginTop: 16}]}>
                <Text style={styles.label}>
                  Số CCCD/CMT <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Nhập số CCCD/CMT"
                  value={identificationId}
                  onChangeText={setIdentificationId}
                  onBlur={() => handleFieldTouch('identificationId')}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  keyboardType="number-pad"
                  maxLength={12}
                  right={<TextInput.Icon icon="card-account-details" />}
                  error={
                    !!(touched.identificationId && errors.identificationId)
                  }
                />
                {touched.identificationId && errors.identificationId ? (
                  <HelperText
                    type="error"
                    visible={
                      !!(touched.identificationId && errors.identificationId)
                    }>
                    {errors.identificationId}
                  </HelperText>
                ) : null}
              </View>

              {/* Full Name */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Họ và tên <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Nhập họ và tên"
                  value={fullName}
                  onChangeText={setFullName}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  right={<TextInput.Icon icon="account" />}
                  error={!!errors.fullName}
                />
                {errors.fullName ? (
                  <HelperText type="error" visible={!!errors.fullName}>
                    {errors.fullName}
                  </HelperText>
                ) : null}
              </View>

              {/* Gender */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>Giới tính</Text>
                <View style={styles.radioGroup}>
                  <TouchableWithoutFeedback onPress={() => setGender('0')}>
                    <View style={styles.radioOption}>
                      <View
                        style={[
                          styles.radioButton,
                          gender === '0' && styles.radioButtonSelected,
                        ]}>
                        {gender === '0' && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                      <Text>Nam</Text>
                    </View>
                  </TouchableWithoutFeedback>

                  <TouchableWithoutFeedback onPress={() => setGender('1')}>
                    <View style={styles.radioOption}>
                      <View
                        style={[
                          styles.radioButton,
                          gender === '1' && styles.radioButtonSelected,
                        ]}>
                        {gender === '1' && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                      <Text>Nữ</Text>
                    </View>
                  </TouchableWithoutFeedback>
                </View>
              </View>

              {/* Email */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Email <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Nhập email"
                  value={email}
                  onChangeText={setEmail}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  right={<TextInput.Icon icon="email" />}
                  error={!!errors.email}
                />
                {errors.email ? (
                  <HelperText type="error" visible={!!errors.email}>
                    {errors.email}
                  </HelperText>
                ) : null}
              </View>

              {/* Phone Number */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Số điện thoại <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Nhập số điện thoại"
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  keyboardType="phone-pad"
                  right={<TextInput.Icon icon="phone" />}
                  error={!!errors.phoneNumber}
                />
                {errors.phoneNumber ? (
                  <HelperText type="error" visible={!!errors.phoneNumber}>
                    {errors.phoneNumber}
                  </HelperText>
                ) : null}
              </View>

              {/* Date of Birth */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Ngày sinh <Text style={styles.required}>*</Text>
                </Text>
                <TouchableWithoutFeedback
                  onPress={() => setShowDatePicker(true)}>
                  <View>
                    <TextInput
                      placeholder="Chọn ngày sinh"
                      value={
                        dateOfBirth
                          ? moment(dateOfBirth).format('DD/MM/YYYY')
                          : ''
                      }
                      editable={false}
                      theme={{roundness: 8}}
                      style={styles.input}
                      underlineColor="transparent"
                      right={<TextInput.Icon icon="calendar" />}
                      error={!!errors.dateOfBirth}
                    />
                  </View>
                </TouchableWithoutFeedback>
                {errors.dateOfBirth ? (
                  <HelperText type="error" visible={!!errors.dateOfBirth}>
                    {errors.dateOfBirth}
                  </HelperText>
                ) : null}
              </View>

              {/* Password */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Mật khẩu <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Nhập mật khẩu"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={secureTextEntry}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  right={
                    <TextInput.Icon
                      icon={secureTextEntry ? 'eye' : 'eye-off'}
                      onPress={() => setSecureTextEntry(!secureTextEntry)}
                    />
                  }
                  error={!!errors.password}
                />
                {errors.password ? (
                  <HelperText type="error" visible={!!errors.password}>
                    {errors.password}
                  </HelperText>
                ) : null}
              </View>

              {/* Confirm Password */}
              <View style={[styles.inputGroup, {marginTop: 8}]}>
                <Text style={styles.label}>
                  Xác nhận mật khẩu <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  placeholder="Xác nhận mật khẩu"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={secureConfirmTextEntry}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  right={
                    <TextInput.Icon
                      icon={secureConfirmTextEntry ? 'eye' : 'eye-off'}
                      onPress={() =>
                        setSecureConfirmTextEntry(!secureConfirmTextEntry)
                      }
                    />
                  }
                  error={!!errors.confirmPassword}
                />
                {errors.confirmPassword ? (
                  <HelperText type="error" visible={!!errors.confirmPassword}>
                    {errors.confirmPassword}
                  </HelperText>
                ) : null}
              </View>

              <Button
                theme={{roundness: 2}}
                style={styles.submitButton}
                mode="contained"
                contentStyle={{marginTop: 4}}
                loading={loading}
                onPress={handleSignup}>
                Đăng ký
              </Button>

              <Button
                style={{marginTop: 12}}
                mode="text"
                onPress={() => navigation.goBack()}>
                Quay lại đăng nhập
              </Button>
            </View>
          </ScrollView>
          <View style={styles.footer}>
            <CopyrightText />
          </View>
        </KeyboardAvoidingView>

        {/* Date Picker */}
        {showDatePicker && (
          <React.Suspense fallback={null}>
            <DatePicker
              modal
              open={showDatePicker}
              date={dateOfBirth || new Date()}
              mode="date"
              title="Chọn ngày sinh"
              confirmText="Xác nhận"
              cancelText="Huỷ"
              maximumDate={new Date()}
              onConfirm={date => {
                setShowDatePicker(false);
                setDateOfBirth(date);
              }}
              onCancel={() => {
                setShowDatePicker(false);
              }}
            />
          </React.Suspense>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingVertical: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 8,
  },
  bgLoginTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: 450,
    zIndex: 1,
  },
  bgCity: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: 238,
    zIndex: 1,
  },
  centered: {
    flex: 1,
    paddingHorizontal: 16,
    zIndex: 2,
  },
  formContainer: {
    flexDirection: 'column',
    backgroundColor: 'rgba(255,255,255,1)',
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderRadius: 20,
    width: '100%',
    maxWidth: 600,
    alignSelf: 'center',
    shadowOffset: {width: 0, height: 12},
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 4,
  },
  inputGroup: {
    gap: 2,
  },
  label: {
    color: '#080b0d',
    fontWeight: '500',
    marginBottom: 2,
  },
  required: {
    color: 'red',
  },
  input: {
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  submitButton: {
    height: 48,
    marginTop: 24,
    justifyContent: 'center',
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  radioGroup: {
    flexDirection: 'row',
    marginTop: 8,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  radioButton: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#2196F3',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  radioButtonSelected: {
    borderColor: '#2196F3',
  },
  radioButtonInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: '#2196F3',
  },
});

export default CitizenSignupScreen;
