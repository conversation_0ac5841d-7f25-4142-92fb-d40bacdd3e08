import React, {useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  TouchableOpacity,
  Text,
} from 'react-native';
import WebView from 'react-native-webview';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {AuthStackParamList} from '../navigation/AuthNavigator';
import Ionicons from 'react-native-vector-icons/Ionicons';

type WebViewScreenRouteProp = RouteProp<AuthStackParamList, 'WebViewScreen'>;

const WebViewScreen = () => {
  const route = useRoute<WebViewScreenRouteProp>();
  const {url, title} = route.params;
  const navigation = useNavigation();
  const webViewRef = useRef<WebView>(null);
  const [canGoBack, setCanGoBack] = useState(false);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => {
            if (canGoBack && webViewRef.current) {
              webViewRef.current.goBack();
            } else {
              navigation.goBack();
            }
          }}
          style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title || 'Dịch vụ công'}</Text>
        <View style={styles.spacer} />
      </View>
      <WebView
        ref={webViewRef}
        source={{uri: url}}
        style={styles.webview}
        renderLoading={() => (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#E53935" />
          </View>
        )}
        startInLoadingState={true}
        onNavigationStateChange={navState => {
          setCanGoBack(navState.canGoBack);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spacer: {
    width: 24,
  },
});

export default WebViewScreen;
