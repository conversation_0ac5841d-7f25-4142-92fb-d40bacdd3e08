/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {
  Alert,
  ImageBackground,
  Keyboard,
  Platform,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import {Button, Text, TextInput} from 'react-native-paper';
import {Assets} from '../assets';
import {API_CONFIG} from '../api/api-config';
import {CopyrightText} from '../components/CopyrightText';
import {useNavigation} from '@react-navigation/native';

const CitizenForgetPasswordScreen = () => {
  const navigation = useNavigation();
  const [email, setEmail] = useState('');
  const [identificationId, setIdentificationId] = useState('');
  const [loading, setLoading] = useState(false);

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Thông báo', 'Vui lòng nhập email');
      return;
    }

    if (!identificationId.trim()) {
      Alert.alert('Thông báo', 'Vui lòng nhập số CCCD/CMT');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Thông báo', 'Email không đúng định dạng');
      return;
    }

    // ID card validation - assumes ID card is 9 or 12 digits
    const idRegex = /^\d{9}(\d{3})?$/;
    if (!idRegex.test(identificationId)) {
      Alert.alert('Thông báo', 'Số CCCD/CMT không đúng định dạng');
      return;
    }

    setLoading(true);

    try {
      // Example API call - replace with your actual endpoint
      const response = await fetch(
        `${API_CONFIG.ENDPOINT}/User/ResetPassword`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            identificationId,
          }),
        },
      );

      const data = await response.json();

      if (response.ok) {
        Alert.alert(
          'Thành công',
          'Yêu cầu đặt lại mật khẩu đã được gửi. Vui lòng kiểm tra email của bạn.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ],
        );
      } else {
        Alert.alert(
          'Lỗi',
          data.message || 'Có lỗi xảy ra khi đặt lại mật khẩu',
        );
      }
    } catch (error) {
      console.error('Reset password error:', error);
      Alert.alert(
        'Lỗi',
        'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        <ImageBackground
          source={Assets.bgGradient}
          style={StyleSheet.absoluteFill}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgLoginTop}
          style={styles.bgLoginTop}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgCity}
          style={styles.bgCity}
          resizeMode="contain"
        />
        <KeyboardAvoidingView
          style={styles.centered}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <ScrollView
            contentContainerStyle={styles.scrollContainer}
            keyboardShouldPersistTaps="handled">
            <View style={styles.formContainer}>
              <Text style={styles.title}>Quên mật khẩu</Text>
              <Text style={styles.subtitle}>
                Nhập thông tin để lấy lại mật khẩu tài khoản của bạn
              </Text>

              <View style={[styles.inputGroup, {marginTop: 16}]}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  placeholder="Nhập email đã đăng ký"
                  value={email}
                  onChangeText={setEmail}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  autoCapitalize="none"
                  keyboardType="email-address"
                  right={<TextInput.Icon icon="email" />}
                />
              </View>

              <View style={[styles.inputGroup, {marginTop: 16}]}>
                <Text style={styles.label}>Số CCCD/CMT</Text>
                <TextInput
                  placeholder="Nhập số CCCD/CMT"
                  value={identificationId}
                  onChangeText={setIdentificationId}
                  theme={{roundness: 8}}
                  style={styles.input}
                  underlineColor="transparent"
                  keyboardType="number-pad"
                  right={<TextInput.Icon icon="card-account-details" />}
                />
              </View>

              <Button
                theme={{roundness: 2}}
                style={styles.submitButton}
                mode="contained"
                contentStyle={{marginTop: 4}}
                loading={loading}
                onPress={handleResetPassword}>
                Gửi yêu cầu
              </Button>

              <Button
                style={{marginTop: 12}}
                mode="text"
                onPress={() => navigation.goBack()}>
                Quay lại đăng nhập
              </Button>
            </View>
          </ScrollView>
          <View style={styles.footer}>
            <CopyrightText />
          </View>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 16,
  },
  bgLoginTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: 450,
    zIndex: 1,
  },
  bgCity: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: 238,
    zIndex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
    zIndex: 2,
  },
  formContainer: {
    flexDirection: 'column',
    backgroundColor: 'rgba(255,255,255,1)',
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
    shadowOffset: {width: 0, height: 12},
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 4,
  },
  inputGroup: {
    gap: 4,
  },
  label: {
    color: '#080b0d',
    fontWeight: '500',
    marginBottom: 2,
  },
  input: {
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  submitButton: {
    height: 48,
    marginTop: 24,
    justifyContent: 'center',
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
});

export default CitizenForgetPasswordScreen;
