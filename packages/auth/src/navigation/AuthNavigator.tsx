import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {NavigationContainer} from '@react-navigation/native';
import SignInScreen from '../screens/SignInScreen';
import CitizenForgetPasswordScreen from '../screens/CitizenForgetPasswordScreen';
import CitizenSignupScreen from '../screens/CitizenSignupScreen';
import CitizenLoginScreen from '../screens/CitizenLogin';
import WebViewScreen from '../screens/WebViewScreen';
import {useConfigStore} from '@ac-mobile/common';

export type AuthStackParamList = {
  SignIn: undefined;
  CitizenLogin: undefined;
  CitizenForgetPassword: undefined;
  CitizenSignup: undefined;
  WebViewScreen: {
    url: string;
    title?: string;
  };
};

const Stack = createNativeStackNavigator<AuthStackParamList>();

const AuthNavigator = () => {
  const {getConfig} = useConfigStore();
  const loginMode = getConfig('loginMode');
  const isOfficerMode = loginMode === 'officer';

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={isOfficerMode ? 'SignIn' : 'CitizenLogin'}
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
        }}>
        <Stack.Screen name="SignIn" component={SignInScreen} />
        <Stack.Screen name="CitizenLogin" component={CitizenLoginScreen} />
        <Stack.Screen
          name="CitizenForgetPassword"
          component={CitizenForgetPasswordScreen}
        />
        <Stack.Screen name="CitizenSignup" component={CitizenSignupScreen} />
        <Stack.Screen
          name="WebViewScreen"
          component={WebViewScreen}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AuthNavigator;
