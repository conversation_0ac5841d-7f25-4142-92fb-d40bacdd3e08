import React from 'react';
import {render} from '@testing-library/react-native';
import {PaperProvider} from 'react-native-paper';
import {SignInScreenMock} from '../mocks/SignInScreen';

// Mock theme for testing
const mockTheme = {
  colors: {
    primary: '#c00014',
    surface: '#ffffff',
    background: '#fffbee',
    onSurface: '#1c1b1d',
    onSurfaceVariant: '#23272a',
    outline: '#e5e7eb',
    primaryContainer: '#ffdad6',
    tertiary: '#106d20',
    shadow: '#000000',
  },
  roundness: 2,
  isV3: false,
};

// Test wrapper with theme provider
const TestWrapper = ({children}: {children: React.ReactNode}) => (
  <PaperProvider theme={mockTheme}>{children}</PaperProvider>
);

describe('SignInScreenMock', () => {
  it('renders correctly with theme', () => {
    const {getByText, getByPlaceholderText} = render(
      <TestWrapper>
        <SignInScreenMock />
      </TestWrapper>
    );

    // Check if form elements are rendered
    expect(getByText('Tài khoản')).toBeTruthy();
    expect(getByText('Mật khẩu')).toBeTruthy();
    expect(getByText('Đăng nhập')).toBeTruthy();
    expect(getByText('Đăng nhập chức năng công dân')).toBeTruthy();
    
    // Check if input fields are rendered
    expect(getByPlaceholderText('Nhập tài khoản')).toBeTruthy();
    expect(getByPlaceholderText('Nhập mật khẩu')).toBeTruthy();
    
    // Check if copyright text is rendered
    expect(getByText('© 2024 iHanoi App')).toBeTruthy();
    expect(getByText('[MOCK_ENDPOINT]')).toBeTruthy();
  });

  it('applies theme colors correctly', () => {
    const {getByText} = render(
      <TestWrapper>
        <SignInScreenMock />
      </TestWrapper>
    );

    // The component should render without errors when theme is applied
    // This test ensures the useTheme hook works properly
    expect(getByText('Đăng nhập')).toBeTruthy();
  });
});
