import React from 'react';
import {render} from '@testing-library/react-native';
import {PaperProvider} from 'react-native-paper';
import {CopyrightText} from '../src/components/CopyrightText';

// Mock theme for testing
const mockTheme = {
  colors: {
    onSurfaceVariant: '#23272a',
  },
  roundness: 2,
  isV3: false,
};

// Test wrapper with theme provider
const TestWrapper = ({children}: {children: React.ReactNode}) => (
  <PaperProvider theme={mockTheme}>{children}</PaperProvider>
);

describe('CopyrightText', () => {
  it('renders correctly with theme', () => {
    const {getByText} = render(
      <TestWrapper>
        <CopyrightText />
      </TestWrapper>
    );

    expect(getByText('© 2024 iHanoi App')).toBeTruthy();
  });

  it('applies theme colors correctly', () => {
    const {getByText} = render(
      <TestWrapper>
        <CopyrightText />
      </TestWrapper>
    );

    const textElement = getByText('© 2024 iHanoi App');
    expect(textElement).toBeTruthy();
    
    // The component should render without errors when theme is applied
    // This ensures the useTheme hook works properly
  });
});
